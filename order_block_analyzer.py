# order_block_analyzer.py

import pandas as pd
from loguru import logger
from typing import Dict

class OrderBlockAnalyzer:
    def __init__(self, logger=logger, ict_analyzer=None):
        self.logger = logger
        self.ict_analyzer = ict_analyzer

    def _calculate_fvg_enhancement_score(self, ob, fvgs, candles=None):
        """
        Verilen OB'nin bir FVG ile ne kadar güçlendirildiğini hesaplar.
        """
        if not self.ict_analyzer or not fvgs:
            return 0
        best_score = 0
        for fvg in fvgs:
            score = self.ict_analyzer.calculate_fvg_enhancement_score(fvg, ob, candles)
            if score > best_score:
                best_score = score
        return best_score

    def find_order_blocks(self, candles: pd.DataFrame, structure_breaks: list, fvgs: list = None) -> Dict[str, list]:
        results = {"bullish": [], "bearish": []}
        if not structure_breaks or candles.empty:
            return results

        # Her kırılım i<PERSON><PERSON> doğru O<PERSON>'yi bul
        for brk in structure_breaks:
            broken_pivot_ts = brk.get('broken_pivot_timestamp')
            if not broken_pivot_ts:
                continue

            try:
                pivot_candle_index = candles[candles['timestamp'] == broken_pivot_ts].index[0]
            except IndexError:
                continue

            search_start_index = max(0, pivot_candle_index - 5)
            search_end_index = pivot_candle_index + 1
            relevant_candles = candles.iloc[search_start_index:search_end_index]

            ob_candle = None
            ob_type = None

            if brk.get('direction') == 'bullish':
                down_candles = relevant_candles[relevant_candles['close'] < relevant_candles['open']]
                if not down_candles.empty:
                    ob_candle = down_candles.iloc[-1]
                    ob_type = 'bullish'
            elif brk.get('direction') == 'bearish':
                up_candles = relevant_candles[relevant_candles['close'] > relevant_candles['open']]
                if not up_candles.empty:
                    ob_candle = up_candles.iloc[-1]
                    ob_type = 'bearish'

            if ob_candle is not None and ob_type is not None:
                fvg_strength = self._determine_ob_strength(candles, ob_candle['timestamp'])
                ob_data = {
                    'timestamp': ob_candle['timestamp'],
                    'open': ob_candle['open'],
                    'high': ob_candle['high'],
                    'low': ob_candle['low'],
                    'close': ob_candle['close'],
                    'type': ob_type,
                    'caused_break_type': brk.get('type'),
                    'strength': fvg_strength
                }
                ob_data['fvg_enhancement_score'] = self._calculate_fvg_enhancement_score(ob_data, fvgs, candles)
                results[ob_type].append(ob_data)

        # Her bir yön için sadece en sonuncuyu al ve ICT uyumlu güncellik kontrolü
        current_time = candles.iloc[-1]['timestamp'] if not candles.empty else None
        max_age_hours = 168  # 168 saat (7 gün) - ICT'de Order Block'lar daha uzun süre geçerli kalabilir
        
        def is_recent_enough(ob_timestamp, current_timestamp, max_hours):
            """Order Block'un yeterince güncel olup olmadığını kontrol eder"""
            if not current_timestamp or not ob_timestamp:
                return True  # Zaman bilgisi yoksa kabul et
            try:
                time_diff = (current_timestamp - ob_timestamp).total_seconds() / 3600
                return time_diff <= max_hours
            except:
                return True  # Hata durumunda kabul et
        
        # Güncel Order Block'ları filtrele - ICT'de Order Block'lar uzun süre geçerli kalabilir
        if results['bullish']:
            recent_bullish = [ob for ob in results['bullish'] 
                            if is_recent_enough(ob['timestamp'], current_time, max_age_hours)]
            if recent_bullish:
                results['bullish'] = [max(recent_bullish, key=lambda x: x['timestamp'])]
                # FVG enhancement puanını ekle
                ob_data['fvg_enhancement_score'] = self._calculate_fvg_enhancement_score(ob_data, fvgs, candles)
            else:
                # Yaş sınırını 2 katına çıkar (14 gün) - ICT'de Order Block'lar uzun süre geçerli
                extended_bullish = [ob for ob in results['bullish'] 
                                  if is_recent_enough(ob['timestamp'], current_time, max_age_hours * 2)]
                if extended_bullish:
                    results['bullish'] = [max(extended_bullish, key=lambda x: x['timestamp'])]
                    logger.info("Bullish Order Block yaş sınırı gevşetildi (14 gün)")
                else:
                    logger.debug("Tüm bullish Order Block'lar çok eski (>14 gün), filtrelendi")
                    results['bullish'] = []
                
        if results['bearish']:
            recent_bearish = [ob for ob in results['bearish'] 
                            if is_recent_enough(ob['timestamp'], current_time, max_age_hours)]
            if recent_bearish:
                results['bearish'] = [max(recent_bearish, key=lambda x: x['timestamp'])]
            else:
                # Yaş sınırını 2 katına çıkar (14 gün) - ICT'de Order Block'lar uzun süre geçerli
                extended_bearish = [ob for ob in results['bearish'] 
                                  if is_recent_enough(ob['timestamp'], current_time, max_age_hours * 2)]
                if extended_bearish:
                    results['bearish'] = [max(extended_bearish, key=lambda x: x['timestamp'])]
                    logger.info("Bearish Order Block yaş sınırı gevşetildi (14 gün)")
                else:
                    logger.debug("Tüm bearish Order Block'lar çok eski (>14 gün), filtrelendi")
                    results['bearish'] = []

        # ... (kodun geri kalanı aynı kalabilir) ...
        # Sonrasında en son ve güncel olanı filtreleme mantığı doğrudur.
        # ...
        # Analiz sonucu her zaman bir liste olmalıdır.
        final_results = {
            'bullish': results['bullish'],
            'bearish': results['bearish'],
        }

        logger.info(f"Found {len(final_results['bullish'])} bullish OBs and {len(final_results['bearish'])} bearish OBs.")
        return final_results

    def _determine_ob_strength(self, candles: pd.DataFrame, ob_timestamp: pd.Timestamp) -> int:
        """
        Bir Order Block'un gücünü, onu takip eden mumlarda FVG (Fair Value Gap)
        oluşup oluşmadığına göre belirler.

        Args:
            candles (pd.DataFrame): Mum verileri.
            ob_timestamp (pd.Timestamp): Order Block'un zaman damgası.

        Returns:
            int: 5 (strong) veya 3 (medium)
        """
        try:
            ob_index = candles.index[candles['timestamp'] == ob_timestamp][0]

            # OB'den sonraki 2 mumu kontrol et (OB'nin kendisi, 1. mum, 2. mum)
            if ob_index + 2 >= len(candles):
                return 3 # Yeterli mum yok - medium

            candle_minus_1 = candles.iloc[ob_index] # The OB candle itself
            candle_plus_1 = candles.iloc[ob_index + 1]
            candle_plus_2 = candles.iloc[ob_index + 2]

            # Bullish OB sonrası FVG (Yükseliş FVG'si)
            # OB'nin (candle -1) high'ı ile 2 sonraki mumun (candle +2) low'u arasında boşluk var mı?
            if candle_minus_1['close'] > candle_minus_1['open']: # Bullish OB'nin kendisi genellikle bearish olur ama genel kural
                if candle_minus_1['high'] < candle_plus_2['low']:
                    logger.debug(f"Strength Check: Strong Bullish OB at {ob_timestamp} due to FVG.")
                    return 5  # Strong

            # Bearish OB sonrası FVG (Düşüş FVG'si)
            # OB'nin (candle -1) low'u ile 2 sonraki mumun (candle +2) high'ı arasında boşluk var mı?
            if candle_minus_1['close'] < candle_minus_1['open']:
                if candle_minus_1['low'] > candle_plus_2['high']:
                    logger.debug(f"Strength Check: Strong Bearish OB at {ob_timestamp} due to FVG.")
                    return 5  # Strong

        except IndexError:
            logger.warning(f"Strength Check: Index error for OB at {ob_timestamp}. Not enough subsequent candles.")
            return 3  # Medium
        except Exception as e:
            logger.error(f"Error in _determine_ob_strength: {e}")
            return 3  # Medium

        return 3  # FVG bulunamazsa medium