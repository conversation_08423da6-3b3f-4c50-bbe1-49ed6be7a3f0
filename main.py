# main.py
import os
import sys
import time
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional

from dotenv import load_dotenv
from loguru import logger

# --- Modül Importları ---
from bybit_client import BybitClient
from silver_bullet_analyzer import SilverBulletAnalyzer
from amd_analyzer import AmdAnalyzer
from killzone_analyzer import KillzoneAnalyzer
from liquidity_analyzer import LiquidityAnalyzer
from fvrp_analyzer import FVRPAnalyzer, analyze_historical_fvrp
from npoc_analyzer import NPOCAnalyzer
from scoring_system import ScoringSystem
from smart_entry_strategy import SmartEntryStrategy
from signal_orchestrator import SignalOrchestrator
from stats_tracker import StatsTracker
from config_manager import load_config
from data_loader import DataLoader
from market_structure_analyzer import MarketStructureAnalyzer
from order_block_analyzer import OrderBlockAnalyzer
from fvg_analyzer import FvgAnalyzer
from ifvg_analyzer import IFVGAnalyzer
from turtle_soup_ifvg_analyzer import TurtleSoupIFVGAnalyzer
from mentorship_model_analyzer import MentorshipModelAnalyzer
from breaker_block_analyzer import BreakerBlockAnalyzer
from displacement_analyzer import DisplacementAnalyzer
from premium_discount_analyzer import PremiumDiscountAnalyzer
from opening_gap_analyzer import OpeningGapAnalyzer
from cvd_analyzer import CVDAnalyzer
# YENİ GELİŞMİŞ ICT MODÜLLER
from rejection_block_analyzer import RejectionBlockAnalyzer
from weak_strong_swings_analyzer import WeakStrongSwingsAnalyzer
from volume_imbalance_analyzer import VolumeImbalanceAnalyzer
from htf_poi_ltf_mss_analyzer import HTFPoiLTFMssAnalyzer
from liquidity_hunt_weak_strong_analyzer import LiquidityHuntWeakStrongAnalyzer
from killzone_session_manipulation_analyzer import KillzoneSessionManipulationAnalyzer
from risk_manager import RiskManager
from alert_manager import AlertManager
from chart_generator import ChartGenerator # YENİ
from session_manager import SessionManager
from invalidation_manager import InvalidationManager
# Diğer gerekli analizörler
from fibonacci_analyzer import FibonacciAnalyzer
from divergence_analyzer import DivergenceAnalyzer
from daily_bias_analyzer import DailyBiasAnalyzer
from supertrend_analyzer import SuperTrendAnalyzer
from timeframe_levels_analyzer import TimeframeLevelsAnalyzer
from vwap_calculator import calculate_vwap
from ict_concepts_analyzer import ICTConceptsAnalyzer
from ote_confluence_analyzer import OTEConfluenceAnalyzer
from fvg_ob_confluence_analyzer import FVGOBConfluenceAnalyzer
from confluence_aggregator import ConfluenceAggregator # YENİ

# --- Log Kurulumu ---
def setup_logging(log_level="INFO"):
    """Loguru için log kurulumunu yapar."""
    logger.remove()
    logger.add(
        sys.stderr,
        level=log_level.upper(),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    )
    logger.add(
        "logs/app_{time:YYYY-MM-DD}.log",
        rotation="1 day",
        retention="7 days",
        level=log_level.upper(),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        encoding='utf-8'
    )
    logger.info("Loglama kurulumu tamamlandı.")

# --- Ana Orkestratör Sınıfı ---
class TradingBotOrchestrator:
    """
    Tüm ticaret botu operasyonlarını yöneten ana sınıf.
    Modülleri başlatır, veri akışını yönetir ve ana analiz döngüsünü çalıştırır.
    """
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.services = {}
        self.analyzers = {}
        self.active_trades = []
        self.last_analysis_times = {}
        self.safe_mode = False # Güvenli mod bayrağı
        self.current_session_state = {} # YENİ: Seans durumunu sembol bazlı takip etmek için



    def initialize_services(self):
        """Gerekli tüm servisleri ve analizörleri başlatır."""
        logger.info("Servisler ve analizörler başlatılıyor...")
        try:
            # Servisler
            # BybitClient'ı burada bir kere oluşturup DataLoader'a veriyoruz
            bybit_client = BybitClient()
            self.services['data_loader'] = DataLoader(bybit_client=bybit_client)

            rm_config = self.config.get('risk_management', {})
            self.services['risk_manager'] = RiskManager(
                capital=rm_config.get('capital', 10000),
                default_risk_pct=rm_config.get('default_risk_pct', 1.0),
                leverage=rm_config.get('leverage', 10),
                maker_fee_pct=rm_config.get('maker_fee_pct', 0.02),
                taker_fee_pct=rm_config.get('taker_fee_pct', 0.05)
            )
            self.services['alert_manager'] = AlertManager(config=self.config)
            self.services['session_manager'] = SessionManager(config=self.config)
            self.services['chart_generator'] = ChartGenerator() # YENİ
            self.services['invalidation_manager'] = InvalidationManager()
            
            # StatsTracker - Sinyal takibi ve performans metrikleri
            stats_config = self.config.get('stats', {})
            self.services['stats_tracker'] = StatsTracker(
                stats_dir=stats_config.get('stats_dir', 'stats'),
                main_timeframe=stats_config.get('main_timeframe', '240'),
                alert_manager=self.services['alert_manager'] # DI: AlertManager'ı enjekte et
            )

            # Analizörler - Merkezi konfigürasyondan ayarları al
            analyzer_config = self.config.get('analyzers', {})
            
            # --- Temel Analizörler (Bağımlılıkları olmayanlar veya temel bağımlılıklar) ---
            from pivot_analyzer import PivotAnalyzer
            self.analyzers['pivot'] = PivotAnalyzer()
            self.analyzers['premium_discount'] = PremiumDiscountAnalyzer()
            self.analyzers['liquidity_hunt_weak_strong'] = LiquidityHuntWeakStrongAnalyzer()
            self.analyzers['fvg'] = FvgAnalyzer()
            self.analyzers['ifvg'] = IFVGAnalyzer()
            self.analyzers['breaker_block'] = BreakerBlockAnalyzer()
            self.analyzers['cvd'] = CVDAnalyzer()
            self.analyzers['fibonacci'] = FibonacciAnalyzer()
            self.analyzers['divergence'] = DivergenceAnalyzer()
            self.analyzers['daily_bias'] = DailyBiasAnalyzer()
            self.analyzers['fvrp'] = FVRPAnalyzer()
            self.analyzers['rejection_blocks'] = RejectionBlockAnalyzer()
            self.analyzers['weak_strong_swings'] = WeakStrongSwingsAnalyzer()
            self.analyzers['volume_imbalance'] = VolumeImbalanceAnalyzer()
            self.analyzers['timeframe_levels'] = TimeframeLevelsAnalyzer()
            self.analyzers['mentorship_model'] = MentorshipModelAnalyzer()
            self.analyzers['silver_bullet'] = SilverBulletAnalyzer(self.services['session_manager'])
            self.analyzers['amd'] = AmdAnalyzer(self.analyzers['pivot'])

            # --- Config-Bağımlı Temel Analizörler ---
            supertrend_config = analyzer_config.get('supertrend', {})
            self.analyzers['supertrend'] = SuperTrendAnalyzer(
                atr_period=supertrend_config.get('atr_period', 10),
                atr_multiplier=supertrend_config.get('atr_multiplier', 3.0),
                use_rma=supertrend_config.get('use_rma', True)
            )
            displacement_config = analyzer_config.get('displacement', {})
            self.analyzers['displacement'] = DisplacementAnalyzer(
                std_length=displacement_config.get('std_length', 100),
                std_multiplier=displacement_config.get('std_multiplier', 4.0)
            )
            opening_gap_config = analyzer_config.get('opening_gap', {})
            self.analyzers['opening_gap'] = OpeningGapAnalyzer(
                min_gap_size_pct=opening_gap_config.get('min_gap_size_pct', 0.01)
            )
            npoc_config = analyzer_config.get('npoc', {})
            self.analyzers['npoc'] = NPOCAnalyzer(
                resolution=npoc_config.get('resolution', 30),
                lookback_periods=npoc_config.get('lookback_periods', 5),
                client=bybit_client
            )
            liqsfp_config = config.get('liqsfp', {})
            liquidity_config = config.get('liquidity', {})
            self.analyzers['liquidity'] = LiquidityAnalyzer(
                hunt_tolerance_percent=liquidity_config.get('hunt_tolerance_pct', 0.2),
                min_swing_strength=liquidity_config.get('min_swing_strength', 3),
                liquidity_expiry_hours=liquidity_config.get('expiry_hours', 72),
                hunt_confirmation_pips=liquidity_config.get('hunt_confirmation_pips', 5.0),
                liqsfp_tolerance_pct=liqsfp_config.get('tolerance_pct', 0.0)
            )
            turtle_soup_config = self.config.get('turtle_soup_ifvg', {})
            self.analyzers['turtle_soup_ifvg'] = TurtleSoupIFVGAnalyzer(config=turtle_soup_config)
            
            # --- Bağımlılıkları Olan Analizörler (Doğru Sırada) ---
            self.analyzers['market_structure'] = MarketStructureAnalyzer(
                mss_sensitivity=analyzer_config.get('market_structure', {}).get('mss_sensitivity', 5),
                msb_sensitivity=analyzer_config.get('market_structure', {}).get('msb_sensitivity', 15),
                pivot_analyzer=self.analyzers['pivot'],
                supertrend_analyzer=self.analyzers['supertrend']
            )
            
            self.analyzers['fvg_ob_confluence'] = FVGOBConfluenceAnalyzer(
                premium_discount_analyzer=self.analyzers['premium_discount'],
                liquidity_hunt_analyzer=self.analyzers['liquidity_hunt_weak_strong']
            )
            
            self.analyzers['ict_concepts'] = ICTConceptsAnalyzer(
                fvg_ob_confluence_analyzer=self.analyzers['fvg_ob_confluence']
            )

            self.analyzers['order_block'] = OrderBlockAnalyzer(
                logger,
                ict_analyzer=self.analyzers['ict_concepts']
            )
            
            ote_config = self.config.get('ote_ob_confluence', {})
            self.analyzers['ote_confluence'] = OTEConfluenceAnalyzer(
                ote_fib_min=ote_config.get('fib_min', 0.618),
                ote_fib_max=ote_config.get('fib_max', 0.79),
                min_confluence_score=ote_config.get('min_quality_score', 70.0),
                proximity_tolerance_pct=ote_config.get('proximity_tolerance_pct', 1.0)
            )

            self.analyzers['htf_poi_ltf_mss'] = HTFPoiLTFMssAnalyzer(
                market_structure_analyzer=self.analyzers['market_structure'],
                fvg_analyzer=self.analyzers['fvg'],
                liquidity_analyzer=self.analyzers['liquidity'],
                premium_discount_analyzer=self.analyzers['premium_discount']
            )
            
            self.analyzers['killzone_session_manipulation'] = KillzoneSessionManipulationAnalyzer(
                market_structure_analyzer=self.analyzers['market_structure'],
                fvg_analyzer=self.analyzers['fvg'],
                order_block_analyzer=self.analyzers['order_block']
            )
            
            self.analyzers['confluence_aggregator'] = ConfluenceAggregator(config=self.config)

            # --- Strateji ve Puanlama Sistemleri ---
            self.services['strategy'] = SmartEntryStrategy(
                config=config,
                ote_confluence_analyzer=self.analyzers['ote_confluence']
            )
            self.services['scoring'] = ScoringSystem(
                stats_tracker=self.services['stats_tracker'],
                smart_entry_strategy=self.services['strategy'],
                premium_discount_analyzer=self.analyzers['premium_discount'],
                ict_concepts_analyzer=self.analyzers['ict_concepts'],
                fibonacci_analyzer=self.analyzers['fibonacci'],
                ote_confluence_analyzer=self.analyzers['ote_confluence'],
                cvd_analyzer=self.analyzers['cvd']  # Sentiment analizi için CVD analyzer inject et
            )
            
            # --- Ana Sinyal Orkestratörü ---
            self.services['signal_orchestrator'] = SignalOrchestrator(
                scoring_system=self.services['scoring'],
                smart_entry_strategy=self.services['strategy'],
                session_manager=self.services['session_manager'],
                stats_tracker=self.services['stats_tracker'],
                confluence_aggregator=self.analyzers['confluence_aggregator']
            )

            logger.info("Tüm servisler ve analizörler başarıyla başlatıldı.")
            logger.success("🚀 Dependency Injection iyileştirmeleri uygulandı - Memory ve CPU optimizasyonu aktif")
        except Exception as e:
            logger.critical(f"Başlatma sırasında kritik hata: {e}", exc_info=True)
            raise

    def run_initial_analyses(self):
        """Uygulama başlarken bir kez çalışacak olan statik analizleri yürütür."""
        logger.info("--- Başlangıç Analizleri Çalıştırılıyor ---")

        # 1. Tarihsel FVRP Analizi
        try:
            logger.info("Adım 1: Tarihsel FVRP analizi başlatılıyor...")
            analyze_historical_fvrp(client=self.services['data_loader'].bybit_client)
            logger.success("Tarihsel FVRP analizi tamamlandı.")
        except Exception as e:
            logger.error(f"Tarihsel FVRP analizi başarısız: {e}", exc_info=True)

        # 2. Naked POC Analizi
        try:
            logger.info("Adım 2: Naked POC analizi başlatılıyor...")
            self.analyzers['npoc'].analyze_all_symbols()
            logger.success("Naked POC analizi tamamlandı.")
        except Exception as e:
            logger.error(f"Naked POC analizi başarısız: {e}", exc_info=True)

        # 3. Key Levels Analizi
        try:
            logger.info("Adım 3: Anahtar Seviye (Günlük, Haftalık, Aylık) analizi başlatılıyor...")
            self.analyzers['timeframe_levels'].analyze_all_symbols()
            logger.success("Anahtar Seviye analizi tamamlandı.")
        except Exception as e:
            logger.error(f"Anahtar Seviye analizi başarısız: {e}", exc_info=True)

        logger.info("--- Başlangıç Analizleri Tamamlandı ---")

    def run_analysis_cycle(self):
        """Ana analiz döngüsünü çalıştırır."""
        cycle_count = 0
        
        while True:
            try:
                cycle_count += 1
                loop_start_time = time.time()
                
                logger.info("="*60)
                logger.info(f"🔄 ANALİZ DÖNGÜSÜ #{cycle_count} BAŞLADI")
                logger.info("="*60)

                # Güvenli mod aktif ise uyarı ver
                if self.safe_mode:
                    logger.warning("🔥 GÜVENLİ MOD AKTİF: Yeni sinyal üretimi durduruldu. Sadece aktif pozisyonlar izleniyor.")

                # Aktif sinyal durumu kontrolü (her zaman çalışır)
                try:
                    stats_tracker = self.services.get('stats_tracker')
                    if stats_tracker:
                        active_signals = stats_tracker.get_active_signals_summary()
                        if active_signals:
                            logger.info(f"📋 Aktif Sinyaller: {len(active_signals)} adet takip ediliyor")
                            # ... (aktif sinyal loglama kısmı aynı kalabilir)

                            logger.info("🔍 Aktif sinyaller kontrol ediliyor...")
                            data_loader = self.services.get('data_loader')
                            if data_loader:
                                active_symbols = list(set(signal.get('symbol') for signal in active_signals if signal.get('symbol')))
                                current_prices = {}
                                all_timeframe_data = {}
                                
                                for symbol in active_symbols:
                                    latest_candle = data_loader.get_latest_candle(symbol)
                                    if latest_candle is not None:
                                        current_prices[symbol] = float(latest_candle.get('close', 0))
                                    
                                    timeframe_data = {}
                                    for tf in ['15', '60', '240', '720', 'D']:
                                        candles = data_loader.get_candles(symbol, tf, limit=100)
                                        if candles is not None and not candles.empty:
                                            logger.debug(f"DEBUG - Aktif sinyal kontrolü: {symbol} {tf} - {len(candles)} mum alındı")
                                            structure_analysis = self.analyzers['market_structure'].analyze(candles)
                                            timeframe_data[tf] = {
                                                'candles': candles,
                                                'structure_analysis': structure_analysis
                                            }
                                    all_timeframe_data[symbol] = timeframe_data
                                
                                completed_signals = stats_tracker.check_active_signals(current_prices, all_timeframe_data)
                                
                                if completed_signals:
                                    logger.success(f"✅ {len(completed_signals)} sinyal güncellendi/tamamlandı")
                                    for signal in completed_signals:
                                        logger.info(f"  • {signal.get('symbol', 'N/A')}: {signal.get('status', 'N/A')}")
                            
                            # Başarılı kontrolden sonra güvenli modu kapat
                            if self.safe_mode:
                                logger.success("✅ Aktif sinyal kontrolü başarıyla tamamlandı. Güvenli mod devre dışı bırakılıyor.")
                                self.safe_mode = False
                        else:
                            logger.info("📋 Aktif Sinyal: Yok")
                            # Aktif sinyal yoksa ve sistem güvenli moddaysa, normale dönebilir.
                            if self.safe_mode:
                                logger.success("✅ Aktif sinyal kalmadı. Güvenli mod devre dışı bırakılıyor.")
                                self.safe_mode = False
                except Exception as e:
                    logger.critical(f"💥 KRİTİK HATA: Aktif sinyal kontrolü başarısız oldu! {e}", exc_info=True)
                    if not self.safe_mode:
                        self.safe_mode = True
                        logger.warning("🔥 SİSTEM GÜVENLİ MODA ALINDI. Yeni sinyal üretimi durduruldu.")
                        self.services['alert_manager'].send_alert(
                            "KRİTİK HATA: Aktif pozisyon yönetimi başarısız oldu! Sistem güvenli moda alındı, yeni pozisyon açılmayacak.",
                            "error"
                        )

                # Sadece güvenli modda değilse yeni sembol analizlerini çalıştır
                analyzed_symbols = 0
                if not self.safe_mode:
                    for symbol in self.config.get('symbols', []):
                        self.analyze_symbol(symbol)
                        analyzed_symbols += 1
                else:
                    logger.info("⏭️ Güvenli mod nedeniyle yeni sembol analizleri atlanıyor.")


                loop_duration = time.time() - loop_start_time
                sleep_duration = max(0, self.config.get('analysis_interval', 60) - loop_duration)
                
                logger.info("="*60)
                logger.success(f"✅ DÖNGÜ #{cycle_count} TAMAMLANDI")
                if analyzed_symbols > 0:
                    logger.info(f"📊 {analyzed_symbols} sembol analiz edildi")
                logger.info(f"⏱️  Süre: {loop_duration:.2f}s | Bekleme: {sleep_duration:.2f}s")
                logger.info("="*60)
                
                time.sleep(sleep_duration)

            except KeyboardInterrupt:
                logger.warning("⚠️  Kullanıcı tarafından durduruldu (Ctrl+C)")
                logger.info("🔄 Sistemi güvenli şekilde kapatılıyor...")
                self.services['alert_manager'].send_alert("Ticaret botu manuel olarak durduruldu.", "shutdown")
                break
            except Exception as e:
                import traceback
                stack_trace = traceback.format_exc()
                logger.error(f"❌ Ana döngüde beklenmedik hata: {e}")
                logger.error(f"Stack trace:\n{stack_trace}")
                self.services['alert_manager'].send_alert(f"KRİTİK HATA: {e}", "error")
                logger.info("⏱️  60 saniye bekleme sonrası yeniden deneme...")
                time.sleep(60) # Hata durumunda bir süre bekle

    def analyze_symbol(self, symbol: str):
        """Belirtilen bir sembol için tam analiz sürecini yürütür."""
        # Güvenli mod kontrolü
        if self.safe_mode:
            logger.warning(f"[{symbol}] 🔥 Güvenli mod aktif, yeni sinyal analizi atlanıyor.")
            return

        logger.info(f"--- {symbol} için analiz başlıyor ---")
        
        # ÖNCE: Bu sembol için zaten aktif sinyal var mı kontrol et
        stats_tracker = self.services.get('stats_tracker')
        if stats_tracker and stats_tracker.has_active_signal_for_symbol(symbol):
            logger.info(f"[{symbol}] ⏭️  Bu sembol için zaten aktif sinyal var, yeni analiz atlanıyor.")
            return
            
        # Soğuma periyodu kontrolü
        if stats_tracker and stats_tracker.is_on_cooldown(symbol):
            logger.info(f"[{symbol}] ❄️  Soğuma periyodunda, yeni analiz atlanıyor.")
            return
        
        logger.info("==================================================")
        logger.info("--- Yeni Analiz & Puanlama Döngüsü Başladı ---")
        all_symbol_data = {}

        # a. Veri Toplama (Session-Aware)
        logger.info(f"[{symbol}] Adım a: Veri Toplama...")

        session_manager = self.services.get('session_manager')
        extra_historical_data = False
        current_session = None

        if session_manager:
            if session_manager.is_london_session():
                current_session = 'london'
            elif session_manager.is_ny_session():
                current_session = 'newyork'
            elif session_manager.is_asia_session():
                current_session = 'asia'

            # Önceki seans durumuyla karşılaştır
            previous_session = self.current_session_state.get(symbol)
            if current_session and current_session != previous_session:
                extra_historical_data = True
                logger.info(f"[{symbol}] Yeni seans başlangıcı tespit edildi: {previous_session} -> {current_session}. Ekstra geçmiş veri çekilecek.")
            
            # Güncel seans durumunu kaydet
            self.current_session_state[symbol] = current_session

        data_loader = self.services['data_loader']
        main_timeframe = self.config.get('main_timeframe', '240')
        candles = data_loader.get_candles(
            symbol,
            timeframe=main_timeframe,
            limit=self.config.get('max_candles', 230),
            session_context=current_session,
            extra_historical_data=extra_historical_data # YENİ
        )
        if candles is None or candles.empty:
            logger.error(f"[{symbol}] Ana zaman dilimi ({main_timeframe}) için mum verileri alınamadı. Analiz atlanıyor.")
            return

        all_symbol_data['candles'] = candles
        
        # Session bilgisini analiz için sakla
        if current_session:
            logger.info(f"[{symbol}] Aktif seans: {current_session.upper()} - Genişletilmiş veri alındı")
            all_symbol_data['current_session'] = current_session
        
        # Eski sistemdeki gibi temel market bilgilerini logla
        current_price = float(candles.iloc[-1]['close'])
        daily_change_pct = ((current_price - float(candles.iloc[0]['open'])) / float(candles.iloc[0]['open'])) * 100
        volume_24h = float(candles['volume'].sum())
        logger.info(f"[{symbol}] Fiyat: {current_price:.2f} | 24s Değişim: {daily_change_pct:+.2f}% | Hacim: {volume_24h:.2f}")
        
        logger.success(f"[{symbol}] Adım a tamamlandı: {len(candles)} mum verisi alındı.")

        # a2. HTF (12h) Veri Toplama - ICT HTF POI Analizi için
        logger.info(f"[{symbol}] Adım a2: HTF (12h) Veri Toplama...")
        htf_12h_candles = data_loader.get_candles(symbol, '720', limit=100)  # 12h HTF data
        if htf_12h_candles is not None and not htf_12h_candles.empty:
            all_symbol_data['htf_12h_candles'] = htf_12h_candles
            logger.success(f"[{symbol}] 12h HTF verisi alındı: {len(htf_12h_candles)} mum")
            
            # 12h HTF Structure Analysis - Tek Doğruluk Kaynağı için belirgin anahtar
            all_symbol_data['htf_structure'] = self.analyzers['market_structure'].analyze(htf_12h_candles)
            htf_12h_pivots = all_symbol_data['htf_structure'].get('major_pivots', [])

            # 12h HTF Order Block Analysis
            all_symbol_data['htf_order_blocks'] = self.analyzers['order_block'].find_order_blocks(
                htf_12h_candles,
                all_symbol_data['htf_structure'].get('breaks', [])
            )

            # 12h HTF FVG Analysis
            all_symbol_data['htf_fvg'] = self.analyzers['fvg'].find_fvgs(
                htf_12h_candles,
                htf_12h_pivots
            )
            
            # 12h HTF Order Block sayısını güvenli şekilde hesapla
            htf_obs = all_symbol_data['htf_order_blocks']
            bullish_obs = htf_obs.get('bullish', []) or []
            bearish_obs = htf_obs.get('bearish', []) or []
            total_obs = len(bullish_obs) + len(bearish_obs)

            logger.success(f"[{symbol}] 12h HTF analizi tamamlandı: "
                          f"Pivots={len(htf_12h_pivots)}, "
                          f"OBs={total_obs}, "
                          f"FVGs={len(all_symbol_data['htf_fvg']) if isinstance(all_symbol_data['htf_fvg'], list) else 0}")
        else:
            logger.warning(f"[{symbol}] 12h HTF verisi alınamadı - HTF POI analizi atlanacak")
            all_symbol_data['htf_12h_candles'] = None

        # b. ICT Çekirdek Analizi
        logger.info(f"[{symbol}] Adım b: ICT Çekirdek Analizi...")

        # DÜZELTME: Ana zaman dilimi (4h) structure analizi - Tek Doğruluk Kaynağı için belirgin anahtar
        logger.debug(f"DEBUG - Ana analiz: {symbol} {main_timeframe} - {len(candles)} mum ile market structure analizi başlatılıyor")
        all_symbol_data['main_tf_structure'] = self.analyzers['market_structure'].analyze(candles)
        major_pivots = all_symbol_data['main_tf_structure'].get('major_pivots', [])
        internal_pivots = all_symbol_data['main_tf_structure'].get('internal_pivots', [])

        # ✅ DOĞRU: BOS sonrası Fibonacci seviyelerini ayrı anahtarda sakla
        logger.debug(f"[{symbol}] BOS Fibonacci analizi yapılıyor...")
        bos_fibonacci_analysis = self.analyzers['fibonacci'].calculate_bos_fibonacci_levels(
            break_data=all_symbol_data['main_tf_structure'].get('latest_bos')
        )
        all_symbol_data['bos_fibonacci_analysis'] = bos_fibonacci_analysis
        all_symbol_data['bos_fib_levels'] = bos_fibonacci_analysis.get('bos_levels', {})

        # DÜZELTME: Likidite analizi için hem major hem de internal pivotları birleştirerek kullan.
        # Bu, daha fazla likidite seviyesinin (BSL/SSL) tespit edilmesini sağlar.
        all_pivots_for_liquidity = major_pivots + internal_pivots
        
        all_symbol_data['liquidity_analysis'] = self.analyzers['liquidity'].analyze(
            candles, 
            all_pivots_for_liquidity, # Sadece major değil, tüm pivotları gönder
            symbol  # Symbol parametresi eklendi
        )

        # FVG analizi artık piyasa yapısı bağlamında yapılıyor
        all_symbol_data['fvg_analysis'] = self.analyzers['fvg'].find_fvgs(candles, major_pivots)
        all_symbol_data['ifvg_analysis'] = self.analyzers['ifvg'].analyze(candles, all_symbol_data['fvg_analysis'])
        all_symbol_data['main_tf_order_blocks'] = self.analyzers['order_block'].find_order_blocks(
            candles,
            all_symbol_data['main_tf_structure'].get('breaks', []),
            fvgs=all_symbol_data.get('fvg_analysis', [])
        )
        all_symbol_data['breaker_block_analysis'] = self.analyzers['breaker_block'].find_breaker_blocks(
            candles, 
            major_pivots
        )
        
        # ✅ DOĞRU: Premium/Discount analizi için ayrı anahtar kullan
        daily_candles = self.services['data_loader'].get_candles(symbol, 'D', limit=240)  # Son 240 günlük veri
        current_price = candles.iloc[-1]['close'] if not candles.empty else 0
        all_symbol_data['fibonacci_analysis'] = self.analyzers['fibonacci'].analyze_premium_discount(daily_candles, current_price)
        
        # Timeframe Levels analizi - Tutarlı isimlendirme için
        main_timeframe = self.config.get('main_timeframe', '240')
        timeframe_levels = self.analyzers['timeframe_levels'].get_levels(symbol, main_timeframe)
        all_symbol_data['timeframe_levels'] = timeframe_levels
        logger.debug(f"[{symbol}] Timeframe levels eklendi: {list(timeframe_levels.keys()) if timeframe_levels else 'Boş'}")
        
        # OTE Levels hesaplama (major pivotlara göre)
        major_pivots = all_symbol_data['main_tf_structure'].get('major_pivots', [])
        if len(major_pivots) >= 2:
            # Son iki swing arasındaki impulse leg için OTE hesapla
            recent_pivots = major_pivots[-2:]
            impulse_start = recent_pivots[0].get('price', 0)
            impulse_end = recent_pivots[1].get('price', 0)
            
            # Direction'u pivot tipine göre belirle
            direction = 'bullish' if impulse_end > impulse_start else 'bearish'
            
            ote_data = self.analyzers['fibonacci'].calculate_ote_levels(impulse_start, impulse_end, direction)
            if not ote_data.get('error'):
                # BOS Fibonacci analizi ile OTE verilerini birleştir
                bos_fibonacci_analysis = all_symbol_data['bos_fibonacci_analysis']
                bos_fibonacci_analysis['ote_levels'] = ote_data
                logger.debug(f"[{symbol}] OTE seviyeleri hesaplandı: {direction} impulse için")
            else:
                logger.warning(f"[{symbol}] OTE hesaplama hatası: {ote_data.get('message', 'Bilinmeyen hata')}")
        else:
            logger.debug(f"[{symbol}] OTE hesaplama için yeterli pivot yok ({len(major_pivots)}/2)")
        
        # OTE + Order Block Confluence Analizi
        if all_symbol_data.get('bos_fib_levels'):
            logger.info(f"[{symbol}] OTE + Order Block confluence analizi başlatılıyor...")
            all_symbol_data['ote_confluence_analysis'] = self.analyzers['ote_confluence'].analyze(
                symbol=symbol,
                fib_levels=all_symbol_data['bos_fib_levels'],
                order_blocks=all_symbol_data.get('order_blocks', []),
                fvgs=all_symbol_data.get('fvg_data', {}).get('all_fvgs', [])
            )
            logger.success(f"[{symbol}] OTE Confluence analizi tamamlandı: {len(all_symbol_data['ote_confluence_analysis']['confluences'])} yüksek kalite confluence")
        
        # h. Gelişmiş ICT Analizleri
        logger.info(f"[{symbol}] Adım h: Gelişmiş ICT Analizleri...")
        
        # Volume Imbalance Analizi
        all_symbol_data['volume_imbalance_analysis'] = self.analyzers['volume_imbalance'].analyze(
            candles, major_pivots
        )
        
        # Rejection Block Analizi
        order_block_data = all_symbol_data.get('main_tf_order_blocks', {})
        bullish_obs = order_block_data.get('bullish', [])
        bearish_obs = order_block_data.get('bearish', [])
        all_order_blocks = bullish_obs + bearish_obs
        
        all_symbol_data['rejection_block_analysis'] = self.analyzers['rejection_blocks'].analyze(
            candles, all_order_blocks
        )
        
        # ESKİ, HATALI Turtle Soup analizi bloğu kaldırıldı. Yenisi aşağıda try-except bloğu içinde çalışıyor.
        
        # SSoT DÜZELTME: Breaker Retest Analizi
        logger.info(f"[{symbol}] Breaker Retest analizi yapılıyor...")
        all_symbol_data['breaker_retest_analysis'] = self.analyzers['breaker_block'].analyze_retest(
            candles=candles,
            breaker_blocks=all_symbol_data['breaker_block_analysis']
        )
        
        # SuperTrend Analizi
        all_symbol_data['supertrend_analysis'] = self.analyzers['supertrend'].analyze_candles(symbol, main_timeframe, candles)
        
        logger.success(f"[{symbol}] Adım h tamamlandı: Gelişmiş ICT analizleri")

        # CVD Analizi (VolumeImbalance'dan önce çalıştır)
        try:
            trades = self.services['data_loader'].bybit_client.get_public_trades(symbol, limit=1000)
            if trades:
                all_symbol_data['cvd_analysis'] = self.analyzers['cvd'].calculate_cvd_from_trades(trades)
                logger.success(f"[{symbol}] CVD analizi tamamlandı.")
            else:
                all_symbol_data['cvd_analysis'] = None
                logger.warning(f"[{symbol}] CVD analizi için işlem verisi alınamadı.")
        except Exception as e:
            logger.error(f"[{symbol}] CVD analizi sırasında hata: {e}", exc_info=True)
            all_symbol_data['cvd_analysis'] = None

        # Open Interest Analizi (CVD ile birlikte sentiment analizi için)
        try:
            oi_data = self.services['data_loader'].bybit_client.get_open_interest(symbol, '4h', limit=50)
            if oi_data:
                all_symbol_data['oi_analysis'] = oi_data
                logger.success(f"[{symbol}] Open Interest analizi tamamlandı.")
            else:
                all_symbol_data['oi_analysis'] = None
                logger.warning(f"[{symbol}] Open Interest verisi alınamadı.")
        except Exception as e:
            logger.error(f"[{symbol}] Open Interest analizi sırasında hata: {e}", exc_info=True)
            all_symbol_data['oi_analysis'] = None

        # Volume Imbalance Analizi (artık CVD sonucunu kullanıyor)
        try:
            all_symbol_data['volume_imbalance'] = self.analyzers['volume_imbalance'].analyze(
                candles,
                fvg_analysis=all_symbol_data.get('fvg_analysis'),
                cvd_analysis=all_symbol_data.get('cvd_analysis') # DI
            )
            logger.success(f"[{symbol}] Volume Imbalance analizi tamamlandı.")
        except Exception as e:
            logger.error(f"[{symbol}] Volume Imbalance analizi sırasında kritik hata: {e}", exc_info=True)
            raise

        # ICT Concepts gelişmiş analizi
        ict_analyzer = self.analyzers['ict_concepts']
        major_pivots = all_symbol_data['main_tf_structure'].get('major_pivots', [])
        current_trend = all_symbol_data['main_tf_structure'].get('current_trend', 'sideways')

        all_symbol_data['valid_swings'] = ict_analyzer.analyze_valid_swings(major_pivots, current_trend)
        # IDM patterns artık market structure'dan alınıyor (merkezileştirildi)
        all_symbol_data['idm_patterns'] = ict_analyzer.get_inducement_from_market_structure(all_symbol_data['main_tf_structure'])
        all_symbol_data['structure_events'] = ict_analyzer.analyze_market_structure_events(candles, major_pivots)
        
        # FVG analysis sonucu liste döndürüyor, onu doğru kullan
        fvg_list = all_symbol_data['fvg_analysis'] if isinstance(all_symbol_data['fvg_analysis'], list) else []
        all_symbol_data['fvg_enhanced_obs'] = ict_analyzer.analyze_fvg_enhanced_order_blocks(
            fvg_list,
            all_symbol_data['main_tf_order_blocks'],
            htf_trend=current_trend,
            current_price=current_price,
            swing_points=all_symbol_data.get('major_pivots', []),
            structure_breaks=all_symbol_data.get('main_tf_structure', {}).get('events', []),
            fibonacci_data=all_symbol_data.get('bos_fibonacci_analysis')
        )
        # 12h HTF verisi varsa ICT Concepts'e ekle
        if all_symbol_data.get('htf_12h_candles') is not None:
            all_symbol_data['swing_analysis_12h'] = {
                'major_pivots': all_symbol_data['htf_structure'].get('major_pivots', [])
            }
            # 24h verisi için Daily kullan
            if daily_candles is not None and not daily_candles.empty:
                daily_structure = self.analyzers['market_structure'].analyze(daily_candles)
                all_symbol_data['swing_analysis_24h'] = {
                    'major_pivots': daily_structure.get('major_pivots', [])
                }
            logger.debug(f"[{symbol}] Multi-timeframe swing verisi ICT Concepts için hazırlandı")
        
        all_symbol_data['premium_signals'] = ict_analyzer.calculate_premium_signals(all_symbol_data)
        
        # YENİ GELİŞMİŞ ICT ANALİZLER
        # Görev 1: Mitigation & Rejection Blocks Analizi
        rejection_analyzer = self.analyzers['rejection_blocks']
        order_blocks = []
        if 'main_tf_order_blocks' in all_symbol_data:
            ob_data = all_symbol_data['main_tf_order_blocks']
            if isinstance(ob_data, dict):
                bullish_obs = ob_data.get('bullish_obs', [])
                bearish_obs = ob_data.get('bearish_obs', [])
                # Liste kontrolü ekle
                if isinstance(bullish_obs, list) and isinstance(bearish_obs, list):
                    order_blocks = bullish_obs + bearish_obs
                    logger.debug(f"[{symbol}] Order Blocks yüklendi: {len(bullish_obs)} bullish + {len(bearish_obs)} bearish = {len(order_blocks)} toplam")
                else:
                    logger.warning(f"[{symbol}] Order blocks veri tipi hatası: bullish_obs={type(bullish_obs)}, bearish_obs={type(bearish_obs)}")
            else:
                logger.warning(f"[{symbol}] Order block analysis veri tipi hatası: {type(ob_data)}")
        
        breaker_blocks = []
        if 'breaker_block_analysis' in all_symbol_data:
            bb_data = all_symbol_data['breaker_block_analysis']
            if isinstance(bb_data, list) and bb_data:
                breaker_blocks = bb_data
                logger.debug(f"[{symbol}] Breaker Blocks yüklendi: {len(breaker_blocks)} toplam")
            elif isinstance(bb_data, dict):
                # Backward compatibility için dict formatını da destekle
                bullish_breakers = bb_data.get('bullish_breakers', [])
                bearish_breakers = bb_data.get('bearish_breakers', [])
                if isinstance(bullish_breakers, list) and isinstance(bearish_breakers, list):
                    breaker_blocks = bearish_breakers + bullish_breakers
                    logger.debug(f"[{symbol}] Breaker Blocks yüklendi: {len(bullish_breakers)} bullish + {len(bearish_breakers)} bearish = {len(breaker_blocks)} toplam")
                else:
                    logger.warning(f"[{symbol}] Breaker blocks veri tipi hatası: bullish_breakers={type(bullish_breakers)}, bearish_breakers={type(bearish_breakers)}")
            else:
                logger.warning(f"[{symbol}] Breaker block analysis veri tipi hatası: {type(bb_data)}")
        
        # Order blocks boş ise, rejection block analizini atla
        if order_blocks:
            all_symbol_data['rejection_block_analysis'] = rejection_analyzer.analyze(
                candles, order_blocks, breaker_blocks
            )
            logger.debug(f"[{symbol}] Rejection Block analizi tamamlandı: {len(order_blocks)} order block kullanıldı")
        else:
            logger.info(f"[{symbol}] Order Block verisi bulunamadı, Rejection Block analizi atlanıyor")
            all_symbol_data['rejection_block_analysis'] = {
                'mitigation_blocks': [],
                'rejection_blocks': [],
                'failed_mitigations': [],
                'active_rejection_zones': [],
                'breaker_confluence': [],
                'summary': {
                    'total_mitigations': 0,
                    'total_rejections': 0,
                    'failed_mitigations': 0,
                    'active_zones': 0,
                    'high_quality_rejections': 0
                },
                'analysis_timestamp': datetime.now().isoformat(),
                'skipped': True
            }
        
        # Görev 2: Power of Three (Po3) - Daily Bias Analyzer'da entegre
        daily_bias_analyzer = self.analyzers['daily_bias']
        if hasattr(daily_bias_analyzer, 'analyze_power_of_three'):
            all_symbol_data['po3_analysis'] = daily_bias_analyzer.analyze_power_of_three(symbol, daily_candles)
            # Combined bias (geleneksel + Po3)
            all_symbol_data['combined_bias'] = daily_bias_analyzer.get_combined_bias(
                symbol, main_timeframe, candles
            )
            logger.info(f"[{symbol}] Power of Three analizi tamamlandı")
        
        # Görev 3: Sinyal Mumu analizi smart_entry_strategy'de gerçekleştirilecek
        # Görev 4: HTF Confluence analizi scoring_system'de gerçekleştirilecek
        
        # LUXALGO SMC ANALİZLER
        # LuxAlgo Görev 1: Zayıf/Güçlü Swing'ler
        ws_analyzer = self.analyzers['weak_strong_swings']
        all_symbol_data['weak_strong_swings'] = ws_analyzer.analyze(
            structure_analysis=all_symbol_data['main_tf_structure'],
            candles=candles
        )
        
        # LuxAlgo Görev 2: Hacim Dengesizliği (Volume Imbalance) - ICT Çekirdek Analizi'nde 'volume_imbalance' olarak yapıldı.
        
        # LuxAlgo Görev 3: Equal Highs/Lows - liquidity_analyzer'da entegre (analyze metodunda zaten çağrılıyor)
        logger.info(f"[{symbol}] LuxAlgo SMC analizleri tamamlandı")
        
        # YENİ: HTF POI + LTF MSS Analizi (Klasik ICT Girişi) - 12h HTF + 1h LTF
        logger.info(f"[{symbol}] HTF POI + LTF MSS analizi başlatılıyor...")
        try:
            # 12h HTF verisi mevcut mu kontrol et
            if all_symbol_data.get('htf_12h_candles') is not None:
                # 1h LTF verisi al (MSS için)
                ltf_1h_candles = data_loader.get_candles(symbol, '60', limit=200)  # 1h LTF data
                
                if ltf_1h_candles is not None and not ltf_1h_candles.empty:
                    # Multi-timeframe veri hazırla (12h HTF + 1h LTF) - ICT Uygun
                    htf_ltf_data = {
                        'htf': all_symbol_data['htf_12h_candles'],  # 12h HTF data
                        'ltf': ltf_1h_candles,  # 1h LTF data (ICT standardı)
                        'htf_timeframe': '720',  # 12h
                        'ltf_timeframe': '60'  # 1h
                    }
                    logger.info(f"[{symbol}] HTF POI + LTF MSS: 12h HTF + 1h LTF kombinasyonu hazırlandı")
                else:
                    logger.warning(f"[{symbol}] 1h LTF verisi alınamadı, HTF POI + LTF MSS analizi atlanıyor")
                    all_symbol_data['htf_poi_ltf_mss'] = {'signals': [], 'error': 'ltf_1h_data_missing'}
                    htf_ltf_data = None
                
                if htf_ltf_data is not None:
                    # HTF POI + LTF MSS analizini çalıştır (12h + 1h ICT standardı)
                    htf_poi_analyzer = self.analyzers['htf_poi_ltf_mss']
                    
                    # Hata düzeltmesi: Analiz için gerekli olan günlük veriyi ekle
                    if 'data' not in all_symbol_data:
                        all_symbol_data['data'] = {}
                    all_symbol_data['data']['D'] = daily_candles

                    all_symbol_data['htf_poi_ltf_mss'] = htf_poi_analyzer.analyze(
                        symbol, htf_ltf_data, all_symbol_data
                    )
                    
                    htf_poi_signals = all_symbol_data['htf_poi_ltf_mss'].get('signals', [])
                    # Sinyallere symbol bilgisini ekle
                    for signal in htf_poi_signals:
                        signal['symbol'] = symbol
                    logger.success(f"[{symbol}] HTF POI + LTF MSS analizi tamamlandı: {len(htf_poi_signals)} sinyal (12h HTF + 1h LTF)")
            else:
                logger.warning(f"[{symbol}] 12h HTF verisi yok, HTF POI + LTF MSS analizi atlanıyor")
                all_symbol_data['htf_poi_ltf_mss'] = {'signals': [], 'error': 'htf_12h_data_missing'}
            
        except Exception as e:
            logger.exception(f"[{symbol}] HTF POI + LTF MSS analizi sırasında beklenmedik bir hata oluştu.")
            all_symbol_data['htf_poi_ltf_mss'] = {'signals': [], 'error': f"{type(e).__name__}: {e}"}
        
        # YENİ: Likidite Avı + Zayıf/Güçlü Swing Analizi (Smart Money Takibi)
        logger.info(f"[{symbol}] Likidite Avı + Zayıf/Güçlü Swing analizi başlatılıyor...")
        try:
            liq_hunt_analyzer = self.analyzers['liquidity_hunt_weak_strong']
            all_symbol_data['liquidity_hunt_weak_strong'] = liq_hunt_analyzer.analyze(
                symbol, all_symbol_data
            )
            
            liq_hunt_signals = all_symbol_data['liquidity_hunt_weak_strong'].get('signals', [])
            # Sinyallere symbol bilgisini ekle
            for signal in liq_hunt_signals:
                signal['symbol'] = symbol
            logger.success(f"[{symbol}] Likidite Avı + Swing analizi tamamlandı: {len(liq_hunt_signals)} sinyal")
            
        except Exception as e:
            logger.error(f"[{symbol}] Likidite Avı + Swing analizi hatası: {e}", exc_info=True)
            all_symbol_data['liquidity_hunt_weak_strong'] = {'signals': [], 'error': str(e)}
        
        # YENİ: Killzone + Seans Açılışı Manipülasyonu Analizi (Zamanlama Temelli ICT)
        logger.info(f"[{symbol}] Killzone + Seans Manipülasyonu analizi başlatılıyor...")
        try:
            # BU STRATEJİ İÇİN ÖZEL OLARAK 15 DAKİKALIK VERİ ÇEK
            ltf_candles_15m = data_loader.get_candles(symbol, '15', limit=200) # Son 50 saatlik veri

            if ltf_candles_15m is not None and not ltf_candles_15m.empty:
                killzone_analyzer = self.analyzers['killzone_session_manipulation']
                all_symbol_data['killzone_session_manipulation'] = killzone_analyzer.analyze(
                    ltf_candles_15m, symbol,  # <-- 4s yerine 15dk'lık veriyi gönder
                    liquidity_data=all_symbol_data.get('liquidity_analysis'),
                    market_structure_data=all_symbol_data.get('main_tf_structure')
                )
            else:
                logger.warning(f"[{symbol}] Killzone analizi için 15dk'lık veri alınamadı.")
                all_symbol_data['killzone_session_manipulation'] = {'signals': [], 'error': 'ltf_15m_data_missing'}
            
            killzone_signals = all_symbol_data['killzone_session_manipulation'].get('signals', [])
            # Sinyallere symbol bilgisini ekle
            for signal in killzone_signals:
                signal['symbol'] = symbol
            active_killzone = all_symbol_data['killzone_session_manipulation'].get('active_killzone')
            
            if active_killzone:
                logger.success(f"[{symbol}] Killzone analizi tamamlandı: {active_killzone['name']}, {len(killzone_signals)} sinyal")
            else:
                logger.info(f"[{symbol}] Killzone analizi tamamlandı: Aktif killzone yok, {len(killzone_signals)} sinyal")
            
        except Exception as e:
            logger.error(f"[{symbol}] Killzone + Seans Manipülasyonu analizi hatası: {e}", exc_info=True)
            all_symbol_data['killzone_session_manipulation'] = {'signals': [], 'error': str(e)}
        
        # YENİ: Turtle Soup + IFVG Analizi (False Breakout + Inverse FVG Confluence)
        logger.info(f"[{symbol}] Turtle Soup + IFVG analizi başlatılıyor...")
        try:
            turtle_soup_analyzer = self.analyzers['turtle_soup_ifvg']
            all_symbol_data['turtle_soup_ifvg'] = turtle_soup_analyzer.analyze(
                symbol=symbol,
                candles=candles,
                structure_analysis=all_symbol_data.get('main_tf_structure', {}),
                ifvg_analysis=all_symbol_data.get('ifvg_analysis', {}),
                liquidity_analysis=all_symbol_data.get('liquidity_analysis', {})
            )
            
            turtle_soup_signals = all_symbol_data['turtle_soup_ifvg'].get('signals', [])
            # Sinyallere symbol bilgisini ekle
            for signal in turtle_soup_signals:
                signal['symbol'] = symbol
            
            total_score = all_symbol_data['turtle_soup_ifvg'].get('total_score', 0)
            logger.success(f"[{symbol}] Turtle Soup + IFVG analizi tamamlandı: {len(turtle_soup_signals)} sinyal (En yüksek skor: {total_score:.1f})")
            
        except Exception as e:
            logger.error(f"[{symbol}] Turtle Soup + IFVG analizi hatası: {e}", exc_info=True)
            all_symbol_data['turtle_soup_ifvg'] = {'signals': [], 'error': str(e)}

        # YENİ: ICT 2022 Mentorship Model Analizi
        logger.info(f"[{symbol}] ICT 2022 Mentorship Model analizi başlatılıyor...")
        try:
            mentorship_analyzer = self.analyzers['mentorship_model']
            all_symbol_data['mentorship_model_signals'] = mentorship_analyzer.analyze(
                symbol, all_symbol_data
            )
            mentorship_signals = all_symbol_data.get('mentorship_model_signals', [])
            if mentorship_signals:
                logger.success(f"[{symbol}] ICT 2022 Mentorship Model analizi tamamlandı: {len(mentorship_signals)} sinyal bulundu.")
            else:
                logger.info(f"[{symbol}] ICT 2022 Mentorship Model analizi tamamlandı: Sinyal koşulları oluşmadı.")
        except Exception as e:
            logger.error(f"[{symbol}] ICT 2022 Mentorship Model analizi hatası: {e}", exc_info=True)
            all_symbol_data['mentorship_model_signals'] = []

        # Silver Bullet Analizi
        logger.info(f"[{symbol}] Silver Bullet analizi başlatılıyor...")
        try:
            silver_bullet_analyzer = self.analyzers['silver_bullet']
            all_symbol_data['silver_bullet_analysis'] = silver_bullet_analyzer.analyze(candles, all_symbol_data['main_tf_structure'].get('major_pivots', []), all_symbol_data.get('fvg_analysis', []), all_symbol_data.get('displacement_analysis', []))
            logger.success(f"[{symbol}] Silver Bullet analizi tamamlandı.")
        except Exception as e:
            logger.error(f"[{symbol}] Silver Bullet analizi sırasında hata: {e}", exc_info=True)
            all_symbol_data['silver_bullet_analysis'] = {}

        # AMD Analizi
        logger.info(f"[{symbol}] AMD analizi başlatılıyor...")
        try:
            amd_analyzer = self.analyzers['amd']
            all_symbol_data['amd_analysis'] = amd_analyzer.analyze(candles, all_symbol_data['main_tf_structure'])
            logger.success(f"[{symbol}] AMD analizi tamamlandı.")
        except Exception as e:
            logger.error(f"[{symbol}] AMD analizi sırasında hata: {e}", exc_info=True)
            all_symbol_data['amd_analysis'] = {}

        # Eski sistemdeki gibi swing sequence logging'i
        major_pivots = all_symbol_data['main_tf_structure'].get('major_pivots', [])
        if major_pivots:
            # Son 5 pivotu al
            last_5_pivots = major_pivots[-5:]
            # Log için formatla: [(candle_index, type, price), ...]
            formatted_pivots = [
                (p.get('candle_index'), p.get('type'), p.get('price')) for p in last_5_pivots
            ]
            logger.info(f"[{symbol}] Son 5 Swing Noktası: {formatted_pivots}")
            logger.debug(f"[{symbol}] Toplam {len(major_pivots)} swing noktası bulundu.")
        
        # Pattern sayısı bilgisi
        patterns_count = len(all_symbol_data.get('patterns', []))
        fvg_count = len(all_symbol_data['fvg_analysis']) if isinstance(all_symbol_data['fvg_analysis'], list) else 0
        
        # IFVG bilgisi
        ifvg_analysis = all_symbol_data.get('ifvg_analysis', {})
        ifvg_count = len(ifvg_analysis.get('inverse_fvgs', [])) if isinstance(ifvg_analysis, dict) else 0
        
        # Turtle Soup + IFVG bilgisi
        turtle_soup_analysis = all_symbol_data.get('turtle_soup_ifvg', {})
        turtle_soup_signals = turtle_soup_analysis.get('signals', [])
        turtle_soup_count = len(turtle_soup_signals)
        
        logger.info(f"[{symbol}] Pattern: {patterns_count}, FVG: {fvg_count}, IFVG: {ifvg_count}, Turtle Soup+IFVG: {turtle_soup_count}")
        
        # Fibonacci analizi için özel log 
        fibonacci_data = all_symbol_data.get('fibonacci_analysis', {})
        if not fibonacci_data.get('error', True):
            zone = fibonacci_data.get('zone', 'Unknown')
            equilibrium = fibonacci_data.get('equilibrium', 0)
            range_high = fibonacci_data.get('range_high', 0)
            range_low = fibonacci_data.get('range_low', 0)
            logger.info(f"[{symbol}] Fibonacci | Bölge:{zone} | Denge:{equilibrium:.4f} | Aralık:{range_low:.4f}-{range_high:.4f}")
        
        logger.success(f"[{symbol}] Adım b tamamlandı: ICT Çekirdek Analizi yapıldı.")

        # c. Confluence Aggregation (YENİ)
        logger.info(f"[{symbol}] Adım c: Confluence Aggregation (Süper POI Analizi)...")
        confluence_aggregator = self.analyzers['confluence_aggregator']
        super_pois = confluence_aggregator.aggregate(all_symbol_data, current_price)
        all_symbol_data['super_pois'] = super_pois

        # OB12_BOS1 analizi
        all_symbol_data['ob12_bos1_analysis'] = confluence_aggregator.analyze_ob12_bos1(symbol, all_symbol_data)

        # YENİ: BOS Tespiti ve Kilit Mekanizmasını Tetikleme
        self._check_and_apply_bos_lock(symbol, all_symbol_data)

        # d, e, f Adımları (Yeni Birleşik Mantık)
        logger.info(f"[{symbol}] Adım d-f: Sinyal Orkestrasyonu, Validasyon ve Emir Hazırlığı...")

        # Orkestratör tüm adımları (yön, POI, confluence, giriş seviyeleri) yönetecek
        final_signal = self.services['signal_orchestrator'].determine_final_signal(symbol, all_symbol_data)

        if not final_signal:
            logger.info(f"[{symbol}] Orkestrasyon sonucu geçerli bir ticaret kurulumu bulunamadı.")
            logger.info(f"--- {symbol} için analiz tamamlandı (Sinyal Yok) ---")
            self._log_detailed_cycle_summary(symbol, all_symbol_data, final_signal=None) # Özet log
            return

        # Sinyal bulunduysa, emir hazırlığı ve takip adımlarına geç
        logger.success(f"[{symbol}] ✅ Geçerli Ticaret Kurulumu Bulundu!")

        # --- Sinyal Zenginleştirme (Alerting için) ---
        logger.debug(f"[{symbol}] Sinyal zenginleştirme adımı başlatılıyor...")
        try:
            # Fiyat Bilgileri
            final_signal['price_info'] = {
                'current_price': current_price,
                'daily_change_pct': daily_change_pct
            }

            # Piyasa Durumu
            structure_1h = self.analyzers['market_structure'].analyze(data_loader.get_candles(symbol, '60', limit=100))
            structure_4h = self.analyzers['market_structure'].analyze(data_loader.get_candles(symbol, '240', limit=100))
            # ✅ DÜZELTME: HTF Rejimi için 12h HTF verisini kullan, 4h değil!
            htf_trend = all_symbol_data.get('htf_structure', {}).get('current_trend', 'sideways')
            htf_regime_state = 'TRENDING_UP' if htf_trend == 'bullish' else ('TRENDING_DOWN' if htf_trend == 'bearish' else 'CHOPPY')

            final_signal['market_context'] = {
                '1h_trend': structure_1h.get('current_trend', 'N/A').upper(),
                '4h_trend': structure_4h.get('current_trend', 'N/A').upper(),
                'htf_regime': htf_trend.upper(),  # 12h HTF trend
                'htf_regime_state': htf_regime_state
            }

            # Likidite ve Diğer Kontekstler
            liquidity_analysis = all_symbol_data.get('liquidity_analysis', {})
            external_liquidity = liquidity_analysis.get('external_liquidity', {})
            bsl_zones = external_liquidity.get('bsl_zones', [])
            ssl_zones = external_liquidity.get('ssl_zones', [])

            nearest_bsl = min(bsl_zones, key=lambda x: abs(x['price'] - current_price)) if bsl_zones else None
            nearest_ssl = min(ssl_zones, key=lambda x: abs(x['price'] - current_price)) if ssl_zones else None

            ifvg_analysis = all_symbol_data.get('ifvg_analysis', {})
            active_ifvgs = [ifvg for ifvg in ifvg_analysis.get('inverse_fvgs', []) if not ifvg.get('filled', False)]

            final_signal['liquidity_context'] = {
                'nearest_bsl': {'price': nearest_bsl['price'], 'distance_pct': ((nearest_bsl['price'] - current_price) / current_price) * 100} if nearest_bsl else {},
                'nearest_ssl': {'price': nearest_ssl['price'], 'distance_pct': ((nearest_ssl['price'] - current_price) / current_price) * 100} if nearest_ssl else {},
                'eqh_count': len(liquidity_analysis.get('equal_levels', {}).get('equal_highs', [])),
                'eql_count': len(liquidity_analysis.get('equal_levels', {}).get('equal_lows', [])),
                'ifvg_summary': f"{len(ifvg_analysis.get('inverse_fvgs', []))} toplam ({len(active_ifvgs)} aktif)"
            }

            # --- Killzone ve Session Bilgilerini Ekle ---
            # Aktif Killzone bilgisini session_manager'dan al
            session_manager = self.services.get('session_manager')
            active_killzone_info = {'name': 'Aktif Değil'}
            if session_manager:
                if session_manager.is_london_session():
                    active_killzone_info = {'name': 'LONDON (08:00-17:00 UTC)'}
                elif session_manager.is_ny_session():
                    active_killzone_info = {'name': 'NEW YORK (12:00-21:00 UTC)'}
                elif session_manager.is_asia_session():
                    active_killzone_info = {'name': 'ASIA (23:00-08:00 UTC)'}
            
            # Killzone analiz sonuçlarından da kontrol et
            killzone_analysis = all_symbol_data.get('killzone_session_manipulation', {})
            if killzone_analysis.get('active_killzone'):
                active_killzone_info = killzone_analysis['active_killzone']

            # Aktif Overlap bilgisi
            active_overlap = 'Aktif Değil'
            if session_manager:
                # London-NY overlap kontrolü
                if session_manager.is_london_session() and session_manager.is_ny_session():
                    active_overlap = 'LONDON-NY OVERLAP (12:00-17:00 UTC)'
                # Diğer overlap'lar da kontrol edilebilir

            # Impact seviyesi hesaplama
            impact_info = {'level': 'MEDIUM', 'score': 50}
            confluence_score = final_signal.get('confluence_score', 0)
            if confluence_score >= 80:
                impact_info = {'level': 'HIGH', 'score': 85}
            elif confluence_score >= 60:
                impact_info = {'level': 'MEDIUM', 'score': 65}
            else:
                impact_info = {'level': 'LOW', 'score': 35}

            # Sinyal önceliği belirleme
            signal_priority = 'MEDIUM'
            if confluence_score >= 75:
                signal_priority = 'HIGH'
            elif confluence_score >= 50:
                signal_priority = 'MEDIUM'
            else:
                signal_priority = 'LOW'

            # --- EK: Telegram için eksik alanları ekle (mevcut veriyi koruyarak) ---
            # Aktif Killzone
            if 'active_killzone' not in final_signal:
                final_signal['active_killzone'] = active_killzone_info
            # Aktif Overlap
            if 'active_overlap' not in final_signal:
                final_signal['active_overlap'] = active_overlap
            # Impact
            if 'impact' not in final_signal:
                final_signal['impact'] = impact_info
            # Sinyal Önceliği
            if 'priority' not in final_signal or final_signal.get('priority') == 'N/A':
                final_signal['priority'] = signal_priority
            # HTF Trend ve Gücü - 12h HTF verisini kullan
            if 'htf_trend' not in final_signal or final_signal.get('htf_trend') == 'N/A':
                final_signal['htf_trend'] = all_symbol_data.get('htf_structure', {}).get('current_trend', 'N/A')
            if 'htf_trend_strength' not in final_signal or final_signal.get('htf_trend_strength') == 0.0:
                final_signal['htf_trend_strength'] = structure_4h.get('trend_strength', 0.0)

            logger.debug(f"[{symbol}] Sinyal zenginleştirme tamamlandı.")
        except Exception as e:
            logger.error(f"[{symbol}] Sinyal zenginleştirme sırasında hata: {e}", exc_info=True)

        # Risk yönetimi ile pozisyon boyutunu hesapla
        risk_manager = self.services['risk_manager']
        entry_price = final_signal.get('primary_entry')
        sl_price = final_signal.get('stop_loss')

        # ✅ GÜVENLIK: Entry ve SL fiyatlarının varlığını kontrol et
        if entry_price is None or sl_price is None:
            logger.error(f"[{symbol}] ❌ Risk hesaplaması için gerekli fiyatlar eksik - Entry: {entry_price}, SL: {sl_price}")
            final_signal['position_size'] = None
        else:
            position_size = risk_manager.calculate_position_size(
                entry_price=entry_price,
                sl_price=sl_price
            )
            final_signal['position_size'] = position_size
            if position_size:
                logger.info(f"[{symbol}] Pozisyon Büyüklüğü Hesaplandı: {position_size:.4f} {symbol}")
            else:
                logger.warning(f"[{symbol}] ⚠️ Pozisyon büyüklüğü hesaplanamadı")

        # Bildirim ve Takip
        alert_manager = self.services['alert_manager']
        stats_tracker = self.services.get('stats_tracker') # StatsTracker'ı al
        chart_generator = self.services.get('chart_generator') # ChartGenerator'ı al

        # Grafik oluştur
        chart_path = None
        if chart_generator:
            chart_path = chart_generator.create_signal_chart(
                symbol=symbol,
                timeframe="4H", # Zaman dilimini manuel olarak ayarla
                candles=candles, # Analiz başında alınan mum verileri
                signal_data=final_signal,
                analysis_data=all_symbol_data # Tüm analiz verilerini gönder
            )

        # Yeni sinyali bildir ve kaydet
        alert_message = alert_manager.format_trade_signal_alert(final_signal)
        alert_manager.send_alert(alert_message, "new_signal", chart_path=chart_path)

        if self.services.get('stats_tracker'):
            signal_id = self.services['stats_tracker'].record_signal(final_signal)
            if signal_id:
                logger.info(f"[{symbol}] ✅ Sinyal başarıyla kaydedildi ve takibe alındı. Signal ID: {signal_id}")
            else:
                logger.error(f"[{symbol}] ❌ Sinyal kaydedilemedi - validasyon başarısız veya hata oluştu.")
                logger.warning(f"[{symbol}] ⚠️ Sinyal verisi: Entry={final_signal.get('primary_entry')}, SL={final_signal.get('stop_loss')}")
        else:
            logger.warning(f"[{symbol}] StatsTracker bulunamadı, sinyal kaydedilemedi.")


        logger.info(f"--- {symbol} için analiz başarıyla tamamlandı (Sinyal Oluşturuldu) ---")
        self._log_detailed_cycle_summary(symbol, all_symbol_data, final_signal=final_signal) # Özet log

    def _check_and_apply_bos_lock(self, symbol: str, all_symbol_data: Dict[str, Any]):
        """
        Yeni bir yapı kırılımı (BOS) olup olmadığını kontrol eder ve varsa,
        geri çekilme beklentisi için bir kilit uygular.
        """
        try:
            structure_analysis = all_symbol_data.get('main_tf_structure', {})
            breaks = structure_analysis.get('breaks', [])
            if not breaks:
                return

            # Sadece en son BOS olayını dikkate al
            last_break = max(breaks, key=lambda b: b.get('timestamp', 0))
            if last_break.get('break_type') != 'BOS':
                return

            stats_tracker = self.services.get('stats_tracker')
            if not stats_tracker:
                return

            # Bu BOS için zaten bir kilit var mı kontrol et
            lock_key = f"{symbol}_{last_break.get('direction')}"
            if stats_tracker.get_active_lock(symbol):
                 # Mevcut kilit bu BOS ile ilgili mi?
                active_lock = stats_tracker.get_active_lock(symbol)
                if active_lock.get('bos_timestamp') == last_break.get('timestamp'):
                    return # Zaten bu BOS için kilit var

            # Geri çekilme için en uygun POI'yi bul
            super_pois = all_symbol_data.get('super_pois', [])
            bos_direction = last_break.get('direction')
            target_pois = [
                p for p in super_pois 
                if p.get('direction') == bos_direction
            ]

            if not target_pois:
                logger.info(f"[{symbol}] Yeni {bos_direction.upper()} BOS tespit edildi ancak geri çekilme için uygun POI bulunamadı.")
                return

            # En yüksek skorlu POI'yi hedef olarak seç
            best_poi = max(target_pois, key=lambda p: p.get('confluence_score', 0))
            
            # Kilidi uygula
            stats_tracker.apply_bos_retracement_lock(symbol, bos_direction, best_poi)

        except Exception as e:
            logger.error(f"[{symbol}] BOS kilit mekanizması hatası: {e}", exc_info=True)

    def _log_analysis_summary(self, symbol: str, all_symbol_data: Dict[str, Any]):
        """
        Eski sistemdeki gibi detaylı analiz özeti logu (sinyal bulunamadığında)
        
        Args:
            symbol: Sembol adı
            all_symbol_data: Tüm analiz verileri
        """
        try:
            candles = all_symbol_data.get('candles')
            if candles is None or candles.empty:
                return
                
            current_price = float(candles.iloc[-1]['close'])
            
            # Daily bias analizi
            daily_bias_result = "Bias Yok"
            structure_analysis = all_symbol_data.get('main_tf_structure', {})
            current_trend = structure_analysis.get('current_trend', 'sideways')
            if current_trend == 'bullish':
                daily_bias_result = "Bullish Bias"
            elif current_trend == 'bearish':
                daily_bias_result = "Bearish Bias"
            
            # SuperTrend durumu
            supertrend_analysis = all_symbol_data.get('supertrend_analysis', {})
            st_direction = supertrend_analysis.get('direction', 'UNKNOWN')
            
            # Pattern ve divergence sayısı
            patterns_count = len(all_symbol_data.get('patterns', []))
            divergences_count = len(all_symbol_data.get('divergences', []))
            
            # FVG bilgisi
            fvg_analysis = all_symbol_data.get('fvg_analysis', [])
            latest_fvg = None
            fvgs = fvg_analysis if isinstance(fvg_analysis, list) else []
            if fvgs:
                latest_fvg = fvgs[-1]  # En son FVG
            
            # Eski format özet log
            daily_change = ((current_price - float(candles.iloc[0]['open'])) / float(candles.iloc[0]['open'])) * 100
            logger.info(f"SEMBOL-ÖZET | {symbol:8} | {current_price:.2f} | {daily_change:+.2f}% | {daily_bias_result} | ST: {st_direction} | {patterns_count} patterns, {divergences_count} divergences")
            
            # FVG detay bilgisi (varsa)
            if latest_fvg:
                fvg_type = latest_fvg.get('type', 'unknown')
                fvg_status = 'Filled' if latest_fvg.get('mitigated', False) else 'Active'
                fvg_top = latest_fvg.get('top', 0)
                fvg_bottom = latest_fvg.get('bottom', 0)
                fvg_eq = (fvg_top + fvg_bottom) / 2
                logger.info(f"[{symbol}] Son FVG: {fvg_type} ({fvg_status}) | Top: {fvg_top:.4f} | Bottom: {fvg_bottom:.4f} | EQ: {fvg_eq:.4f}")
            
            # IDM patterns özeti
            idm_patterns = all_symbol_data.get('idm_patterns', [])
            if idm_patterns:
                idm_count = len(idm_patterns)
                confirmed_idm = len([idm for idm in idm_patterns if idm.get('quality') == 'IDM_CONFIRMED'])
                logger.info(f"[{symbol}] IDM Patterns: {idm_count} toplam, {confirmed_idm} IDM_CONFIRMED")
            
            # Market structure events özeti
            structure_events = all_symbol_data.get('structure_events', {})
            events = structure_events.get('events', [])
            if events:
                recent_events = events[-3:]  # Son 3 event
                event_types = [e.get('type', 'UNKNOWN') for e in recent_events]
                logger.info(f"[{symbol}] Son Market Structure Events: {', '.join(event_types)}")
                
        except Exception as e:
            logger.error(f"[{symbol}] Analiz özeti oluşturma hatası: {e}", exc_info=True)

    def _ensure_dict_type(self, data: Any, default_key: str = "unknown") -> Dict[str, Any]:
        """Veriyi dict tipine dönüştürür, eğer değilse boş dict döndürür."""
        if isinstance(data, dict):
            return data
        elif data is None:
            return {}
        else:
            logger.warning(f"Beklenen dict tipi, {type(data)} alındı. Boş dict ile değiştiriliyor. Key: {default_key}")
            return {}

    def _log_detailed_cycle_summary(self, symbol: str, all_symbol_data: Dict[str, Any], 
                                   final_signal: Optional[Dict[str, Any]] = None):
        """
        Gelişmiş döngü sonu özet logu - Önerilen format
        
        Her sembol analizi sonunda kapsamlı piyasa durumu ve karar özeti
        """
        try:
            from datetime import datetime
            import pytz
            
            candles = all_symbol_data.get('candles')
            if candles is None or candles.empty:
                return
                
            # Temel bilgiler
            current_price = float(candles.iloc[-1]['close'])
            daily_open = float(candles.iloc[0]['open'])
            daily_change_pct = ((current_price - daily_open) / daily_open) * 100
            
            # Zaman bilgisi
            utc_now = datetime.now(pytz.UTC)
            time_str = utc_now.strftime("%d-%m-%Y %H:%M")
            
            # Market structure analizi - Ana TF (4h) için
            main_tf_structure = self._ensure_dict_type(all_symbol_data.get('main_tf_structure', {}), 'main_tf_structure')
            current_trend = main_tf_structure.get('current_trend', 'sideways').upper()

            # ✅ DÜZELTME: HTF Rejimi için 12h HTF verisini kullan, 4h değil!
            htf_structure = self._ensure_dict_type(all_symbol_data.get('htf_structure', {}), 'htf_structure')
            htf_trend = htf_structure.get('current_trend', 'sideways').upper()
            market_direction = htf_trend
            if market_direction == 'SIDEWAYS':
                market_direction = 'NEUTRAL'
                market_state = 'CHOPPY'
            elif market_direction == 'BULLISH':
                market_state = 'TRENDING_UP'
            elif market_direction == 'BEARISH':
                market_state = 'TRENDING_DOWN'
            else:
                market_direction = 'UNKNOWN'
                market_state = 'UNKNOWN'
            
            # Session analizi
            session_manager = self.services.get('session_manager')
            current_session = "Unknown"
            if session_manager:
                is_london = session_manager.is_london_session()
                is_ny = session_manager.is_ny_session()
                is_asia = session_manager.is_asia_session()
                
                if is_ny:
                    current_session = "NEW YORK (12:00-21:00 UTC)"
                elif is_london:
                    current_session = "LONDON (08:00-17:00 UTC)"
                elif is_asia:
                    current_session = "ASIA (23:00-08:00 UTC)"
                else:
                    current_session = "MARKET CLOSED"
            
            # Liquidity seviyeleri
            liquidity_analysis = self._ensure_dict_type(all_symbol_data.get('liquidity_analysis', {}), 'liquidity_analysis')
            nearest_bsl = "N/A"
            nearest_ssl = "N/A"
            
            # Yeni unified format'tan BSL/SSL bilgilerini al
            external_liquidity = self._ensure_dict_type(liquidity_analysis.get('external_liquidity', {}), 'external_liquidity')
            bsl_zones = external_liquidity.get('bsl_zones', [])
            ssl_zones = external_liquidity.get('ssl_zones', [])
            
            # Debug log: Kaç adet liquidity zone bulundu
            logger.debug(f"[{symbol}] Liquidity Debug: BSL zones={len(bsl_zones)}, SSL zones={len(ssl_zones)}")
            
            # BSL (Buy Side Liquidity) - Swing high'ların üzerindeki seviyeleri
            if bsl_zones:
                closest_bsl = min(bsl_zones, key=lambda x: abs(x['price'] - current_price))
                bsl_distance = ((closest_bsl['price'] - current_price) / current_price) * 100
                nearest_bsl = f"{closest_bsl['price']:.4f} ({bsl_distance:+.2f}%)"
                
            # SSL (Sell Side Liquidity) - Swing low'ların altındaki seviyeleri  
            if ssl_zones:
                closest_ssl = min(ssl_zones, key=lambda x: abs(x['price'] - current_price))
                ssl_distance = ((closest_ssl['price'] - current_price) / current_price) * 100
                nearest_ssl = f"{closest_ssl['price']:.4f} ({ssl_distance:+.2f}%)"
            
            # YENİ: EQH/EQL Bilgisi
            equal_levels = self._ensure_dict_type(liquidity_analysis.get('equal_levels', {}), 'equal_levels')
            eqh_count = len(equal_levels.get('equal_highs', []))
            eql_count = len(equal_levels.get('equal_lows', []))
            eq_summary = f"EQH: {eqh_count}, EQL: {eql_count}"
            
            # YENİ: IFVG Bilgisi
            ifvg_analysis = self._ensure_dict_type(all_symbol_data.get('ifvg_analysis', {}), 'ifvg_analysis')
            ifvg_count = len(ifvg_analysis.get('inverse_fvgs', []))
            active_ifvgs = [ifvg for ifvg in ifvg_analysis.get('inverse_fvgs', []) if not ifvg.get('filled', False)]
            ifvg_summary = f"IFVG: {ifvg_count} toplam ({len(active_ifvgs)} aktif)"

            # YENİ: Gelişmiş ICT Analiz Özetleri
            ws_analysis = self._ensure_dict_type(all_symbol_data.get('weak_strong_swings', {}), 'weak_strong_swings')
            ws_swings = ws_analysis.get('classified_swings', [])
            last_ws_swing_summary = "N/A"
            if ws_swings:
                last_ws_swing = ws_swings[-1]
                last_ws_swing_summary = f"{last_ws_swing.get('strength_type', 'UNKNOWN')} @ {last_ws_swing.get('price', 0):.2f}"

            vi_analysis = self._ensure_dict_type(all_symbol_data.get('volume_imbalance', {}), 'volume_imbalance')
            active_vis = vi_analysis.get('active_imbalances', [])
            high_quality_vis = [vi for vi in active_vis if vi.get('quality_score', 0) >= 7]
            vi_summary = f"Aktif VI: {len(active_vis)} (Yüksek Kalite: {len(high_quality_vis)})";

            rejection_analysis = self._ensure_dict_type(all_symbol_data.get('rejection_block_analysis', {}), 'rejection_block_analysis')
            rejection_signals = rejection_analysis.get('signals', [])
            rejection_summary = f"Rejection Sinyali: {len(rejection_signals)}"
            
            po3_analysis = self._ensure_dict_type(all_symbol_data.get('po3_analysis', {}), 'po3_analysis')
            po3_bias_info = po3_analysis.get('po3_bias', {})
            po3_direction = po3_bias_info.get('direction', 'N/A')
            po3_quality = po3_analysis.get('quality', 'N/A')
            po3_summary = f"{po3_direction} (Kalite: {po3_quality})";
            
            # YENİ: HTF POI + LTF MSS Analiz Özeti
            htf_poi_analysis = self._ensure_dict_type(all_symbol_data.get('htf_poi_ltf_mss', {}), 'htf_poi_ltf_mss')
            htf_poi_signals = htf_poi_analysis.get('signals', [])
            htf_poi_count = len(htf_poi_signals)
            
            if htf_poi_count > 0:
                best_poi_signal = max(htf_poi_signals, key=lambda x: x.get('confluence_score', 0))
                poi_confluence_score = best_poi_signal.get('confluence_score', 0)
                poi_summary = f"HTF POI+LTF MSS: {htf_poi_count} sinyal (En iyi: {poi_confluence_score:.1f}/100)"
            else:
                poi_summary = "HTF POI+LTF MSS: Sinyal bulunamadı"
            
            # YENİ: Turtle Soup + IFVG Analiz Özeti
            turtle_soup_analysis = self._ensure_dict_type(all_symbol_data.get('turtle_soup_ifvg', {}), 'turtle_soup_ifvg')
            turtle_soup_signals = turtle_soup_analysis.get('signals', [])
            turtle_soup_count = len(turtle_soup_signals)
            
            if turtle_soup_count > 0:
                best_turtle_signal = max(turtle_soup_signals, key=lambda x: x.get('confluence_score', 0))
                turtle_confluence_score = best_turtle_signal.get('confluence_score', 0)
                turtle_direction = best_turtle_signal.get('direction', 'UNKNOWN')
                turtle_summary = f"Turtle Soup+IFVG: {turtle_soup_count} sinyal ({turtle_direction}, Skor: {turtle_confluence_score:.1f}/100)"
            else:
                turtle_summary = "Turtle Soup+IFVG: Sinyal bulunamadı"
            
            # Final sinyal durumu
            signal_result = "SİNYAL YOK"
            signal_score = "0.0"
            if final_signal:
                signal_type = final_signal.get('type', 'UNKNOWN')
                # ✅ DÜZELTME: strategy_used anahtarını kullan (smart_entry_strategy.py ile tutarlı)
                signal_strategy = final_signal.get('strategy_used', signal_type)
                signal_direction = final_signal.get('direction', 'UNKNOWN')
                signal_confidence = final_signal.get('confidence', 0.0)
                signal_result = f"{signal_strategy} {signal_direction.upper()}"
                signal_score = f"{signal_confidence:.2f}"
            
            # Zengin özet logu
            logger.info(f"""
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 {symbol} | {time_str} UTC DÖNGÜ ÖZETİ
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 Fiyat: {current_price:,.2f} ({daily_change_pct:+.2f}% günlük)
📈 1H Trend: {current_trend}
🎯 HTF Rejim: {market_direction} ({market_state})
⏰ Aktif Killzone: {current_session}
🔴 En Yakın BSL: {nearest_bsl}
🟢 En Yakın SSL: {nearest_ssl}
🔵 Likidite Yapısı: {eq_summary}
🔄 Inverse FVG: {ifvg_summary}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🧠 GELİŞMİŞ ANALİZLER:
   - Son Swing Tipi: {last_ws_swing_summary}
   - Hacim Dengesizliği: {vi_summary}
   - Reddedilme Blokları: {rejection_summary}
   - Power of Three: {po3_summary}
   - {poi_summary}
   - {turtle_summary}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 SONUÇ: {signal_result} | Güven: {signal_score}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━""")
            
        except Exception as e:
            import traceback
            logger.error(f"[{symbol}] Detaylı döngü özeti hatası: {e}")
            logger.error(f"[{symbol}] Hata detayı: {traceback.format_exc()}")
            logger.error(f"[{symbol}] all_symbol_data keys: {list(all_symbol_data.keys()) if all_symbol_data else 'None'}")

    def _log_level_status(self, symbol: str, all_symbol_data: Dict[str, Any]):
        """
        Eski sistemdeki LEVEL-DURUM mantığını yeniden oluşturur
        
        Args:
            symbol: Sembol adı
            all_symbol_data: Tüm analiz verileri
        """
        try:
            candles = all_symbol_data.get('candles')
            if candles is None or candles.empty:
                return
                
            current_price = float(candles.iloc[-1]['close'])
            
            # Fibonacci seviyelerine göre konum analizi
            fibonacci_data = self._ensure_dict_type(all_symbol_data.get('fibonacci_analysis', {}), 'fibonacci_analysis')
            
            logger.info(f"[LEVEL-DURUM] {symbol} Fiyat Durumu:")
            
            # Premium/Discount analizi
            if not fibonacci_data.get('error', True):
                zone = fibonacci_data.get('zone', 'Unknown')
                equilibrium = fibonacci_data.get('equilibrium', 0)
                if equilibrium > 0:
                    eq_diff_pct = ((current_price - equilibrium) / equilibrium) * 100
                    logger.info(f"[LEVEL-DURUM-FIB] Fibonacci'ya göre: {eq_diff_pct:+.2f}% (Bölge: {zone})")
            
            
            # VWAP seviyesi (eğer varsa)
            # Bu bilgi eski log'dan çıkarılabilir ama şu an VWAP calculator'ı yok
            
            # Premium/Discount analizi
            fibonacci_analysis = self._ensure_dict_type(all_symbol_data.get('fibonacci_analysis', {}), 'fibonacci_analysis')
            if not fibonacci_analysis.get('error', True):
                zone = fibonacci_analysis.get('zone', 'Unknown')
                logger.info(f"[LEVEL-DURUM-PD] Premium/Discount: {zone}")
            
            logger.info("--------------------------------------------------")
            logger.info("[LEVEL-SİSTEM-BİTİŞ] Seviye durum analizi tamamlandı")
            
        except Exception as e:
            logger.error(f"[{symbol}] Level durum analizi hatası: {e}", exc_info=True)

# --- Ana Çalıştırma Bloğu ---
if __name__ == "__main__":
    load_dotenv()
    config = load_config()
    setup_logging(config.get("log_level", "INFO"))

    try:
        # Sistem başlangıç mesajları
        logger.info("="*80)
        logger.info("🤖 AUTOMATON-ICT TRADING BOT v1.3 BAŞLATILUYOR")
        logger.info("="*80)
        logger.info("📋 Sistem Konfigürasyonu Yükleniyor...")
        
        # Sembol listesi bilgilendirmesi
        symbols = config.get('symbols', [])
        logger.success(f"✅ {len(symbols)} sembol aktif liste: {', '.join(symbols)}")
        
        # Analiz aralığı bilgisi
        analysis_interval = config.get('analysis_interval', 60)
        logger.info(f"⏱️  Analiz Döngüsü: {analysis_interval} saniye aralıklarla")
        
        # Session bilgileri
        sessions_enabled = config.get('sessions', {}).get('enabled', False)
        if sessions_enabled:
            logger.info("🌍 Killzone Session yönetimi: AKTİF")
        else:
            logger.info("🌍 Killzone Session yönetimi: PASİF")
        
        # Risk yönetimi bilgileri
        risk_config = config.get('risk_management', {})
        capital = risk_config.get('capital', 10000)
        risk_pct = risk_config.get('default_risk_pct', 1.0)
        logger.info(f"💰 Risk Yönetimi: ${capital:,.0f} sermaye, %{risk_pct} risk")
        
        logger.info("-"*80)
        logger.info("🔧 Sistem Bileşenleri Başlatılıyor...")
        
        orchestrator = TradingBotOrchestrator(config)
        orchestrator.initialize_services()

        # --- YENİ EKLENECEK KOD BAŞLANGICI ---
        # StatsReporter'ı başlat ve AlertManager'a bağla
        if config.get("telegram_enabled", False):
            from stats_reporter import StatsReporter
            from notification_service import NotificationService

            # Bildirim servisini oluştur
            notification_service = NotificationService(
                config=config, 
                stats_tracker=orchestrator.services['stats_tracker'])

            # Raporlayıcıyı oluştur
            stats_reporter = StatsReporter(orchestrator.services['stats_tracker'], notification_service.telegram)

            # Raporlayıcıyı bildirim servisine bağla
            notification_service.stats_reporter = stats_reporter

            # Raporlama döngüsünü başlat
            stats_reporter.schedule_reporter()

            # AlertManager'ı yeni bildirim servisi ile güncelle
            orchestrator.services['alert_manager'].telegram_notifier = notification_service.telegram
        # --- YENİ EKLENECEK KOD SONU ---

        logger.info("="*80)
        logger.success("🚀 SİSTEM BAŞARILA AKTİF HALE GETİRİLDİ!")
        logger.info("="*80)

        orchestrator.run_initial_analyses() # Başlangıç analizlerini çalıştır

        logger.info("="*80)
        logger.success("🎯 ANA ANALİZ DÖNGÜSÜ BAŞLATILUYOR...")
        logger.info(f"📊 {len(symbols)} sembol için sürekli analiz başlıyor...")
        logger.info("="*80)

        orchestrator.run_analysis_cycle()
        
    except Exception as e:
        logger.critical(f"❌ KRITIK HATA: Uygulama başlatılamadı: {e}", exc_info=True)
        # Gerekirse burada da bir bildirim gönderilebilir.
        sys.exit(1)