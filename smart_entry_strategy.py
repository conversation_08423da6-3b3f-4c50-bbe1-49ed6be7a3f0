from typing import Dict, Any, Optional, List
import pandas as pd
from loguru import logger
from ote_confluence_analyzer import OTEConfluenceAnalyzer
from utils import format_price_standard

class SmartEntryStrategy:
    """
    Farklı piyasa rejimleri ve volatilite koşullarına göre akıllı giriş,
    stop-loss ve take-profit seviyeleri hesaplayan gelişmiş strateji sınıfı.
    YENİ VERSİYON: İdeal giriş uzaksa veya bulunamazsa piyasa fiyatından girme (fallback) özelliği eklendi.
    YENİ SL MANTIĞI: Yapısal stop-loss için %2-%3 arası "güvenli bölge" kuralı eklendi.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None, ote_confluence_analyzer=None):
        """
        SmartEntryStrategy sınıfını başlatır ve tüm strateji parametrelerini merkezileştirir.

        Args:
            config: Konfigürasyon sözlüğü (config_manager'dan)
            ote_confluence_analyzer: OTEConfluenceAnalyzer instance (opsiyonel, DI için)
        """
        logger.info("Akıllı Giriş Stratejisi modülü başlatıldı")

        # Config'i sakla
        self.config = config or {}

        # Dependency Injection: OTEConfluenceAnalyzer
        if ote_confluence_analyzer is not None:
            self.ote_analyzer = ote_confluence_analyzer
            logger.debug("OTEConfluenceAnalyzer DI ile enjekte edildi")
        else:
            # Lazy initialization - eğer dışarıdan verilmezse kendi oluştur
            ote_config = self.config.get('ote_ob_confluence', {})
            self.ote_analyzer = OTEConfluenceAnalyzer(
                ote_fib_min=ote_config.get('fib_min', 0.618),
                ote_fib_max=ote_config.get('fib_max', 0.79),
                min_confluence_score=ote_config.get('min_quality_score', 70.0),
                proximity_tolerance_pct=ote_config.get('proximity_tolerance_pct', 1.0)
            )
            logger.debug("OTEConfluenceAnalyzer lazy initialization ile oluşturuldu")

        # OTE+OB Confluence parametrelerini config'den al (backward compatibility)
        self._load_ote_config(self.config.get('ote_ob_confluence', {}))
        
        # === GİRİŞ AYARLARI ===
        self.max_entry_distance_pct = 0.01  # İdeal girişin fiyattan en fazla %1.0 uzakta olmasına izin ver.
        self.corrective_min_distance_pct = 0.003  # Corrective rejimde minimum %0.3 mesafe gerekli

        # === YENİ ve GÜNCELLENMİŞ STOP-LOSS PARAMETRELERİ ===
        self.structural_sl_min_risk_pct = 0.02  # YENİ: Yapısal SL için kabul edilebilir minimum risk (%2).
        self.structural_sl_max_risk_impulsive_pct = 0.03  # İmpulsive rejim için maksimum risk (%3).
        self.structural_sl_max_risk_corrective_pct = 0.05  # Corrective rejim için maksimum risk (%5).
        self.default_sl_pct = 0.025             # Varsayılan (fallback) stop-loss yüzdesi (%2.5).
        self.structure_sl_buffer_pct = 0.001    # Yapısal stop'a eklenen tampon payı (%0.1).
        self.min_pivot_distance_pct = 0.002    # YENİ: Pivot seçimi için minimum mesafe (%0.2)

        # === TAKE PROFIT SİSTEM KONFİGÜRASYONU ===
        self.tp1_rr_ratio = 1.0
        self.tp1_5_rr_ratio = 1.5
        self.tp2_rr_ratio = 2.0
        self.tp3_rr_ratio = 3.0
    

    
    def _load_ote_config(self, ote_config: Dict) -> None:
        """OTE konfigürasyonunu yükler - DRY prensibi için ortak fonksiyon"""
        self.ote_min_intersection_pct = ote_config.get('min_intersection_pct', 30)
        self.ote_sweet_spot_weight = ote_config.get('sweet_spot_weight', 74.0)
        self.ote_min_quality_score = ote_config.get('min_quality_score', 50)
        self.ote_max_distance_pct = ote_config.get('max_distance_from_current_pct', 2.0)
    
    def _log_strategy_usage(self, symbol: str, strategy_name: str) -> None:
        """Strategy kullanımını loglar - DRY prensibi için ortak fonksiyon"""
        logger.debug(f"[{symbol}] {strategy_name} giriş mantığı kullanılıyor.")
    
    def _log_info(self, symbol: str, message: str) -> None:
        """Info level log mesajı - DRY prensibi için ortak fonksiyon"""
        logger.info(f"[{symbol}] {message}")
    
    def _log_warning(self, symbol: str, message: str) -> None:
        """Warning level log mesajı - DRY prensibi için ortak fonksiyon"""
        logger.warning(f"[{symbol}] {message}")
    
    def _log_error(self, symbol: str, message: str, exc_info: bool = False) -> None:
        """Error level log mesajı - DRY prensibi için ortak fonksiyon"""
        logger.error(f"[{symbol}] {message}", exc_info=exc_info)
    
    def _log_debug(self, symbol: str, message: str) -> None:
        """Debug level log mesajı - DRY prensibi için ortak fonksiyon"""
        logger.debug(f"[{symbol}] {message}")
    
    def _log_success(self, symbol: str, message: str) -> None:
        """Success level log mesajı - DRY prensibi için ortak fonksiyon"""
        logger.success(f"[{symbol}] {message}")

    def _get_float_levels(self, fibonacci_data: Optional[Dict[str, Any]]) -> Dict[float, Dict[str, Any]]:
        """
        Fibonacci verilerindeki 'levels' sözlüğünü güvenli bir şekilde {float: data} formatına çevirir.
        """
        float_levels = {}
        if not fibonacci_data or not isinstance(fibonacci_data.get('levels'), dict):
            return float_levels

        for key, value in fibonacci_data['levels'].items():
            try:
                float_key = float(key)
                float_levels[float_key] = value
            except (ValueError, TypeError):
                continue
        return float_levels

    def calculate_entry_levels(self, symbol: str, stats: Dict[str, Any],
                               trade_direction: str,
                               pattern_details: Dict[str, Any],
                               fibonacci_data: Optional[Dict[str, Any]] = None,
                               order_blocks: Optional[Dict[str, Any]] = None,
                               fvg_data: Optional[List[Dict[str, Any]]] = None,
                               swing_points: Optional[List[Dict[str, Any]]] = None,
                               candles: Optional[pd.DataFrame] = None,
                               liquidity_data: Optional[Dict[str, Any]] = None,
                               # --- YENİ PARAMETRELERİ EKLE ---
                               premium_discount_data: Optional[Dict[str, Any]] = None,
                               all_symbol_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        GELİŞTİRİLDİ: Gelen sinyalin türüne göre uygun giriş seviyesi hesaplama
        metodunu çağıran bir yönlendirici (dispatcher) olarak çalışır.
        """
        signal_type = pattern_details.get('type', '')
        signal_pattern = pattern_details.get('pattern', signal_type)

        # ✅ DEBUG: Gelen sinyal detaylarını logla
        logger.info(f"[{symbol}] 🎯 SmartEntry: Sinyal Tipi='{signal_type}', Pattern='{signal_pattern}', Detaylar={list(pattern_details.keys())}")
        self._log_info(symbol, f"Giriş seviyesi hesaplanıyor. Sinyal Tipi: {signal_type}, Pattern: {signal_pattern}")

        # 1. Killzone Session Manipulation Sinyalleri
        if 'KILLZONE' in signal_type or 'SESSION_MANIPULATION' in signal_type or pattern_details.get('killzone_data'):
            self._log_strategy_usage(symbol, "Killzone Session Manipulation")
            return self._calculate_killzone_session_entry(symbol, stats, trade_direction, pattern_details, swing_points)

        # 2. HTF POI + LTF MSS Kombinasyonu (Öncelikli kontrol)
        elif 'HTF_POI_LTF_MSS' in signal_pattern or 'HTF_POI_LTF_MSS' in signal_type or pattern_details.get('htf_poi_data'):
            self._log_strategy_usage(symbol, "HTF POI + LTF MSS")
            return self._calculate_htf_poi_ltf_mss_entry(symbol, stats, trade_direction, pattern_details, candles)

        # 3. Yapısal Kırılım Sinyalleri (BOS/MSS) - sadece gerçek BOS/MSS sinyalleri
        elif ('BOS' in signal_type or 'MSS' in signal_type) and 'HTF_POI_LTF_MSS' not in signal_pattern and pattern_details.get('break_data'):
            logger.debug(f"[{symbol}] Yapısal Kırılım (BOS/MSS) giriş mantığı kullanılıyor.")
            return self._calculate_bos_entry(
                symbol, stats, trade_direction, pattern_details,
                fibonacci_data, order_blocks, fvg_data, swing_points, candles, liquidity_data, premium_discount_data, all_symbol_data
            )

        # 4. Monday Range Reversal Sinyali - Özel işleyici
        elif signal_type == 'MONDAY_RANGE_REVERSAL':
            logger.debug(f"[{symbol}] Monday Range Reversal giriş mantığı kullanılıyor.")
            return self._calculate_monday_range_entry(symbol, stats, trade_direction, pattern_details, swing_points=swing_points)

        # 5. Likidite Bazlı Sinyaller (LIQSFP)
        elif 'LIQSFP_REV' in signal_pattern or ('REVERSAL' in signal_type and signal_type != 'MONDAY_RANGE_REVERSAL') or 'HUNT' in signal_type:
            logger.debug(f"[{symbol}] Likidite Avı Dönüş (LIQSFP_REV) giriş mantığı kullanılıyor.")
            return self._calculate_liqsfp_reversal_entry(symbol, stats, trade_direction, pattern_details, candles=candles, swing_points=swing_points, liquidity_data=liquidity_data)

        # 5. Likidite Avı + Zayıf/Güçlü Swing Konfluansı
        elif 'LIQUIDITY_HUNT_WEAK_STRONG' in signal_pattern or pattern_details.get('liquidity_hunt_data'):
            logger.debug(f"[{symbol}] Likidite Avı + Zayıf/Güçlü Swing giriş mantığı kullanılıyor.")
            return self._calculate_liquidity_hunt_weak_strong_entry(symbol, stats, trade_direction, pattern_details, candles)

        # 6. Equal Highs/Lows Breakout Sinyalleri
        elif 'EQUAL_HIGHS' in signal_type or 'EQUAL_LOWS' in signal_type or 'EQH' in signal_type or 'EQL' in signal_type:
            logger.debug(f"[{symbol}] Equal Highs/Lows breakout giriş mantığı kullanılıyor.")
            return self._calculate_equal_highs_lows_entry(symbol, stats, trade_direction, pattern_details, swing_points)

        # 7. Volume Imbalance Sinyalleri
        elif 'VOLUME_IMBALANCE' in signal_type or 'VI_CONFLUENCE' in signal_type:
            logger.debug(f"[{symbol}] Volume Imbalance confluence giriş mantığı kullanılıyor.")
            return self._calculate_volume_imbalance_entry(symbol, stats, trade_direction, pattern_details, swing_points)

        # 8. Rejection Block Sinyalleri  
        elif 'REJECTION_BLOCK' in signal_type or 'MITIGATION_REJECTION' in signal_type:
            logger.debug(f"[{symbol}] Rejection Block giriş mantığı kullanılıyor.")
            return self._calculate_rejection_block_entry(symbol, stats, trade_direction, pattern_details, swing_points)

        # YENİ: FVG + Order Block Confluence Sinyalleri
        elif 'FVG_OB_CONFLUENCE' in signal_type:
            logger.debug(f"[{symbol}] FVG + Order Block Confluence giriş mantığı kullanılıyor.")
            return self._calculate_fvg_ob_confluence_entry(symbol, stats, trade_direction, pattern_details, swing_points)

        # YENİ: Turtle Soup + IFVG Confluence Sinyalleri
        elif 'TURTLE_SOUP_IFVG' in signal_type or 'TURTLE_SOUP_IFVG_CONFLUENCE' in signal_pattern:
            self._log_strategy_usage(symbol, "Turtle Soup + IFVG Confluence")
            return self._calculate_turtle_soup_ifvg_entry(symbol, stats, trade_direction, pattern_details, swing_points)

        # YENİ: Super POI Retest Sinyalleri (eski BREAKER_RETEST)
        elif 'SUPER_POI_RETEST' in signal_type:
            self._log_strategy_usage(symbol, "Super POI Retest")
            return self._calculate_breaker_block_entry(symbol, stats, trade_direction, pattern_details, swing_points)

        # YENİ: Gerçek Breaker Block Retest Sinyalleri
        elif 'BREAKER_BLOCK_RETEST' in signal_type:
            self._log_strategy_usage(symbol, "Breaker Block Retest")
            return self._calculate_breaker_block_retest_entry(symbol, stats, trade_direction, pattern_details, swing_points)

        # Desteklenmeyen sinyal türü - Fallback stratejisi
        else:
            logger.warning(f"[{symbol}] ⚠️ Desteklenmeyen sinyal türü: '{signal_type}' / '{signal_pattern}'")
            logger.warning(f"[{symbol}] 📋 Mevcut pattern_details anahtarları: {list(pattern_details.keys())}")

            # ✅ FALLBACK: Temel giriş stratejisi (sistem çökmesini önlemek için)
            last_price = stats.get("last_price", 0)
            if last_price > 0:
                logger.info(f"[{symbol}] 🔄 Fallback stratejisi devreye giriyor - Mevcut fiyat: {last_price}")

                # Basit stop loss hesaplama (%2.5)
                if trade_direction.lower() in ['bull', 'bullish', 'long']:
                    stop_loss = last_price * 0.975  # %2.5 aşağıda
                    take_profit = last_price * 1.05  # %5 yukarıda
                else:
                    stop_loss = last_price * 1.025  # %2.5 yukarıda
                    take_profit = last_price * 0.95  # %5 aşağıda

                return {
                    "primary_entry": last_price,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                    "strategy_used": "fallback_market_price",
                    "risk_reward_ratio": 2.0
                }
            else:
                logger.error(f"[{symbol}] ❌ Fallback stratejisi için geçerli fiyat bulunamadı")
                return {"primary_entry": None, "strategy_used": "unsupported"}
    
    def _calculate_bos_entry(self, symbol: str, stats: Dict[str, Any],
                            trade_direction: str, pattern_details: Dict[str, Any],
                            fibonacci_data: Optional[Dict[str, Any]] = None,
                            order_blocks: Optional[Dict[str, Any]] = None,
                            fvg_data: Optional[List[Dict[str, Any]]] = None,
                            swing_points: Optional[List[Dict[str, Any]]] = None,
                            candles: Optional[pd.DataFrame] = None,
                            liquidity_data: Optional[Dict[str, Any]] = None,
                            premium_discount_data: Optional[Dict[str, Any]] = None,
                            all_symbol_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        BOS/MSS sinyalleri için hiyerarşik Fibonacci tabanlı giriş stratejisi.
        ICT metodolojisine uygun olarak, Fibonacci seviyelerini dışarıdan alır.
        
        Hiyerarşi:
        1. Öncelik: OTE (%61.8-79) + POI Confluence (İdeal)
        2. İyi: Diğer Fibonacci seviyeleri + POI Confluence (İyi)
        3. Kabul edilebilir: Çıplak Fibonacci seviyeleri (Kabul edilebilir)
        """
        try:
            last_price = stats.get("last_price")
            entry_levels = {"primary_entry": None, "strategy_used": "none", "hierarchy_level": 0}
            
            if not last_price or not pattern_details:
                self._log_error(symbol, "BOS entry için gerekli veriler eksik")
                return entry_levels
            
            # Fibonacci verilerini kontrol et - DI prensibi gereği dışarıdan gelmeli
            if not fibonacci_data or fibonacci_data.get('error'):
                self._log_error(symbol, "BOS entry için Fibonacci verileri bulunamadı veya hatalı")
                return entry_levels
                
            self._log_info(symbol, "🎯 HİYERARŞİK FİBONACCİ GİRİŞ STRATEJİSİ başlatılıyor...")
            
            # Fibonacci seviyelerini dışarıdan gelen veriden al
            full_fib_levels = self._get_float_levels(fibonacci_data)
            if not full_fib_levels:
                self._log_error(symbol, "Fibonacci seviyeleri parse edilemedi")
                return entry_levels
            
            # OTE seviyelerini fibonacci_data'dan çıkar (BOS için özel Fibonacci verisi)
            ote_levels = fibonacci_data.get('ote_zone') or fibonacci_data.get('ote')
            if not ote_levels or ote_levels.get('error'):
                self._log_warning(symbol, "OTE seviyeleri fibonacci_data'dan çıkarılamadı")
                return entry_levels
            
            # Fibonacci seviyelerini logla
            self._log_fibonacci_levels(symbol, full_fib_levels)

            # ============================================================================
            # LEVEL 1 (EN YÜKSEK ÖNCELİK): OTE + POI CONFLUENCE
            # ============================================================================
            logger.info(f"[{symbol}] 🥇 LEVEL 1: OTE + POI Confluence analizi...")
            # ✅ DOĞRU: Önceden analiz edilmiş OTE confluence verisini tüket
            ote_confluence_analysis = all_symbol_data.get('ote_confluence_analysis', {})
            if ote_confluence_analysis and not ote_levels.get('error'):
                ote_confluence = self._consume_ote_confluence_results(
                    symbol, ote_confluence_analysis, trade_direction
                )
                if ote_confluence and ote_confluence.get('confluence_score', 0) >= 60:
                    logger.success(f"[{symbol}] ✅ LEVEL 1 BAŞARILI: OTE + POI Confluence! Skor: {ote_confluence['confluence_score']:.1f}/100")
                    entry_levels.update(ote_confluence)
                    entry_levels['hierarchy_level'] = 1
                    entry_levels['quality_rating'] = 'EXCELLENT'
                    return self._finalize_bos_entry(entry_levels, swing_points, trade_direction, liquidity_data)

            # ============================================================================
            # LEVEL 2 (İYİ): DİĞER FİBONACCİ SEVİYELERİ + POI CONFLUENCE
            # ============================================================================
            logger.info(f"[{symbol}] 🥈 LEVEL 2: Diğer Fibonacci seviyeleri + POI Confluence...")
            priority_levels = [('0.5', 'Equilibrium'), ('0.382', 'Standard Retracement')]
            best_level_confluence = None
            best_level_score = 0

            for level_key, level_name in priority_levels:
                level_price = full_fib_levels.get(level_key)
                if level_price:
                    level_confluence = self._find_level_poi_confluence(
                        symbol, level_price, level_key, order_blocks, fvg_data, trade_direction, stats
                    )
                    if level_confluence and level_confluence.get('confluence_score', 0) > best_level_score:
                        best_level_score = level_confluence.get('confluence_score', 0)
                        best_level_confluence = level_confluence
                        best_level_confluence['level_name'] = level_name

            if best_level_confluence and best_level_score >= 50:
                logger.success(f"[{symbol}] ✅ LEVEL 2 BAŞARILI: {best_level_confluence['level_name']} + POI! Skor: {best_level_score:.1f}/100")
                entry_levels.update(best_level_confluence)
                entry_levels['hierarchy_level'] = 2
                entry_levels['quality_rating'] = 'GOOD'
                return self._finalize_bos_entry(entry_levels, swing_points, trade_direction, liquidity_data)

            # ============================================================================
            # LEVEL 3 (KABULEDİLEBİLİR): ÇIPLAK FİBONACCİ SEVİYELERİ
            # ============================================================================
            logger.info(f"[{symbol}] 🥉 LEVEL 3: Çıplak Fibonacci seviyeleri...")
            naked_fibonacci_result = self._finalize_bos_entry_naked_fib(
                symbol, full_fib_levels, stats.get('last_price', 0), trade_direction, swing_points, liquidity_data
            )
            if naked_fibonacci_result and naked_fibonacci_result.get('primary_entry'):
                logger.info(f"[{symbol}] ✅ LEVEL 3 KULLANILIYOR: Çıplak Fibonacci seviyesi ({naked_fibonacci_result.get('level_key')})")
                entry_levels.update(naked_fibonacci_result)
                entry_levels['hierarchy_level'] = 3
                entry_levels['quality_rating'] = 'ACCEPTABLE'
                return entry_levels
                
            # ============================================================================
            # FALLBACK: GELENEKSEL YAKLAŞIM
            # ============================================================================
            logger.warning(f"[{symbol}] ⚠️ Hiyerarşik strateji başarısız, fallback stratejisi devreye giriyor...")
            fallback_result = self._fallback_bos_entry(
                symbol, stats, trade_direction, pattern_details, order_blocks, fvg_data, swing_points
            )
            fallback_result['hierarchy_level'] = 4
            fallback_result['quality_rating'] = 'FALLBACK'
            return fallback_result

        except Exception as e:
            logger.error(f"[{symbol}] Hiyerarşik BOS giriş stratejisi hatası: {e}", exc_info=True)
            return {"primary_entry": None, "strategy_used": "error", "hierarchy_level": 0}

    def _finalize_bos_entry(self, entry_levels: Dict[str, Any],
                           swing_points: Optional[List[Dict[str, Any]]],
                           trade_direction: str,
                           liquidity_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        BOS giriş seviyelerini tamamlar (SL/TP hesaplama).
        YENİ: Liquidity data desteği eklendi.
        """
        primary_entry = entry_levels.get('primary_entry')
        if not primary_entry:
            return entry_levels
        
        sl_tp_levels = self._calculate_sl_tp(
            entry_price=primary_entry,
            trade_direction=trade_direction,
            swing_points=swing_points,
            regime='IMPULSIVE',
            liquidity_data=liquidity_data
        )
        entry_levels.update(sl_tp_levels)
        return entry_levels

    def _finalize_bos_entry_naked_fib(self, symbol: str, fib_levels: Dict[str, Any], current_price: float,
                                      trade_direction: str, swing_points: Optional[List[Dict[str, Any]]],
                                      liquidity_data: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """Çıplak Fibonacci seviyeleri kullanarak giriş noktası belirler."""
        if not fib_levels:
            return None
            
        priority_order = ['0.705', '0.618', '0.786', '0.5']
        
        for level_key in priority_order:
            level_price = fib_levels.get(level_key)
            if level_price:
                # Girişin mevcut fiyattan mantıklı bir uzaklıkta olduğunu kontrol et
                is_valid_entry = (trade_direction.lower() == 'bullish' and level_price < current_price) or \
                                 (trade_direction.lower() == 'bearish' and level_price > current_price)
                
                if is_valid_entry:
                    best_level = {
                        'primary_entry': level_price,
                        'strategy_used': f'naked_fibonacci_{level_key}',
                        'level_key': level_key
                    }
                    sl_tp = self._calculate_sl_tp(level_price, trade_direction, swing_points, 'IMPULSIVE', 
                                                liquidity_data=liquidity_data)
                    best_level.update(sl_tp)
                    return best_level
        return None

    
    def _find_level_poi_confluence(self, symbol: str, level_price: float, level_key: str,
                                  order_blocks: Optional[Dict[str, Any]], 
                                  fvg_data: Optional[List[Dict[str, Any]]],
                                  trade_direction: str, stats: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Belirli bir Fibonacci seviyesi etrafında POI confluence arar."""
        try:
            tolerance_range = level_price * 0.01
            best_poi = None
            best_score = 0

            # Order Block confluence
            if order_blocks:
                ob = order_blocks.get('bullish') if trade_direction.lower() == 'bullish' else order_blocks.get('bearish')
                if ob:
                    ob_mid = (ob['high'] + ob['low']) / 2
                    if abs(ob_mid - level_price) <= tolerance_range:
                        proximity_score = max(0, 50 - (abs(ob_mid - level_price) / tolerance_range) * 50) if tolerance_range > 0 else 25
                        level_bonus = self._get_fibonacci_level_bonus(level_key)
                        total_score = proximity_score + 30 + level_bonus
                        if total_score > best_score:
                            best_score = total_score
                            best_poi = {
                                'primary_entry': ob_mid,
                                'strategy_used': f'fib_{level_key}_ob_confluence',
                                'poi_type': 'order_block',
                                'confluence_score': total_score
                            }

            # FVG confluence
            if fvg_data and best_score < 60:
                for fvg in fvg_data:
                    fvg_mid = (fvg['top'] + fvg['bottom']) / 2
                    if abs(fvg_mid - level_price) <= tolerance_range:
                        proximity_score = max(0, 40 - (abs(fvg_mid - level_price) / tolerance_range) * 40) if tolerance_range > 0 else 20
                        quality_score = 25 if not fvg.get('mitigated', False) else 12
                        level_bonus = self._get_fibonacci_level_bonus(level_key)
                        total_score = proximity_score + quality_score + level_bonus
                        if total_score > best_score:
                            best_score = total_score
                            best_poi = {
                                'primary_entry': fvg_mid,
                                'strategy_used': f'fib_{level_key}_fvg_confluence',
                                'poi_type': 'fvg',
                                'confluence_score': total_score
                            }
            
            if best_poi and best_score >= 40:
                logger.debug(f"[{symbol}] Fibonacci {level_key} POI confluence: {best_poi['poi_type']} (Skor: {best_score:.1f})")
                return best_poi
            return None
        except Exception as e:
            logger.error(f"[{symbol}] Seviye POI confluence analizi hatası: {e}", exc_info=True)
            return None

    def _get_fibonacci_level_bonus(self, level_key: str) -> float:
        """Fibonacci seviyesinin önemine göre bonus puan verir."""
        bonuses = {'0.618': 20, '0.705': 18, '0.5': 15, '0.786': 12, '0.66': 10, '0.382': 8}
        return bonuses.get(level_key, 5)

    def _calculate_fvg_ob_confluence_entry(self, symbol: str, stats: Dict[str, Any],
                                          trade_direction: str, pattern_details: Dict[str, Any],
                                          swing_points: Optional[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        Gerçek ICT konseptine göre FVG + Order Block Confluence giriş hesaplama.
        
        ICT Kuralları:
        - BOS/MSS swing aralığı içinde FVG ve OB confluence
        - Spatial confluence (aynı fiyat bölgesi) kontrolü
        - OTE enhancement bonus
        - Optimal giriş seviyesi hesaplama
        """
        try:
            current_price = stats.get('last_price', 0)
            if not current_price:
                self._log_error(symbol, "Güncel fiyat bilgisi bulunamadı")
                return {"primary_entry": None, "strategy_used": "fvg_ob_confluence_no_price"}
            
            # Yeni ICT confluence verilerini al
            confluence_details = pattern_details.get('details', {})
            if not confluence_details:
                self._log_warning(symbol, "FVG_OB_CONFLUENCE sinyali için confluence verisi bulunamadı")
                return {"primary_entry": None, "strategy_used": "fvg_ob_confluence_no_data"}
            
            # ICT confluence'dan giriş seviyesini al
            entry_price = confluence_details.get('entry_price')
            if not entry_price:
                self._log_warning(symbol, "Confluence'da giriş seviyesi bulunamadı")
                return {"primary_entry": None, "strategy_used": "fvg_ob_confluence_no_entry"}
            
            # Confluence kalite kontrolü
            confluence_score = confluence_details.get('confluence_score', 0)
            is_ote_enhanced = confluence_details.get('is_ote_enhanced', False)
            spatial_score = confluence_details.get('spatial_score', 0)
            
            self._log_info(symbol, f"ICT FVG-OB Confluence - Giriş: {entry_price:.4f}, Skor: {confluence_score:.1f}")
            
            if is_ote_enhanced:
                self._log_success(symbol, f"OTE Enhanced Confluence! Bonus puanlar alındı")
            
            # ICT Konseptine Göre: Mesafe sınırı yok, confluence geçerliyse kullanılır
            distance_pct = abs(entry_price - current_price) / current_price * 100
            
            # ICT confluence giriş seviyesi - mesafe sınırı olmadan
            entry_levels = {
                "primary_entry": entry_price,
                "strategy_used": "ict_fvg_ob_confluence_pure",
                "confluence_score": confluence_score,
                "spatial_score": spatial_score,
                "is_ote_enhanced": is_ote_enhanced,
                "distance_pct": distance_pct,
                "swing_range": confluence_details.get('swing_range', {})
            }
            
            strategy_quality = "OTE Enhanced" if is_ote_enhanced else "Standard"
            self._log_success(symbol, f"ICT {strategy_quality} Confluence giriş: {entry_price:.4f} (Mesafe: {distance_pct:.2f}%)")
            
            # Stop-Loss ve Take-Profit hesapla
            # ICT confluence için özel SL/TP mantığı
            regime = 'OTE_CONFLUENCE' if is_ote_enhanced else 'ICT_CONFLUENCE'
            sl_tp_levels = self._calculate_sl_tp(
                entry_price=entry_levels["primary_entry"],
                trade_direction=trade_direction,
                swing_points=swing_points,
                regime=regime
            )
            entry_levels.update(sl_tp_levels)
            
            return entry_levels
            
        except Exception as e:
            self._log_error(symbol, f"ICT FVG-OB confluence giriş hesaplama hatası: {e}", exc_info=True)
            return {"primary_entry": None, "strategy_used": "ict_fvg_ob_confluence_error"}
    
    def _calculate_turtle_soup_ifvg_entry(self, symbol: str, stats: Dict[str, Any],
                                        trade_direction: str, pattern_details: Dict[str, Any],
                                        swing_points: Optional[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        Turtle Soup + IFVG confluence için giriş hesaplar.
        
        Turtle Soup + IFVG Stratejisi:
        - False breakout (Turtle Soup) + Inverse FVG confluence
        - Yüksek olasılıklı reversal setup
        - Optimal entry timing
        """
        try:
            current_price = stats.get('last_price', 0)
            if not current_price:
                self._log_error(symbol, "Güncel fiyat bilgisi bulunamadı")
                return {"primary_entry": None, "strategy_used": "turtle_soup_ifvg_no_price"}
            
            # Turtle Soup ve IFVG verilerini al
            turtle_soup_data = pattern_details.get('turtle_soup_data', {})
            ifvg_data = pattern_details.get('ifvg_data', {})
            confluence_score = pattern_details.get('confluence_score', 0)
            
            if not turtle_soup_data or not ifvg_data:
                self._log_warning(symbol, "Turtle Soup veya IFVG verisi eksik")
                return {"primary_entry": None, "strategy_used": "turtle_soup_ifvg_incomplete_data"}
            
            self._log_info(symbol, f"Turtle Soup + IFVG confluence giriş hesaplanıyor (Skor: {confluence_score:.1f})")
            
            # Optimal entry seviyesini hesapla
            entry_price = self._calculate_turtle_soup_ifvg_optimal_entry(
                turtle_soup_data, ifvg_data, current_price, trade_direction
            )
            
            if not entry_price:
                self._log_warning(symbol, "Turtle Soup + IFVG optimal entry hesaplanamadı")
                return {"primary_entry": None, "strategy_used": "turtle_soup_ifvg_no_entry"}
            
            # Stop Loss hesapla (Turtle Soup invalidation seviyesi)
            stop_loss = self._calculate_turtle_soup_ifvg_stop_loss(
                turtle_soup_data, ifvg_data, entry_price, trade_direction
            )
            
            # Take Profit seviyeleri hesapla
            tp_levels = self._calculate_turtle_soup_ifvg_take_profits(
                turtle_soup_data, ifvg_data, entry_price, stop_loss, trade_direction
            )
            
            entry_levels = {
                "primary_entry": entry_price,
                "stop_loss": stop_loss,
                "strategy_used": "turtle_soup_ifvg_confluence",
                "confluence_score": confluence_score,
                "setup_strength": pattern_details.get('setup_strength', 5.0),
                "counter_trend_reversal": pattern_details.get('counter_trend_reversal', False)
            }
            
            # TP seviyelerini ekle
            entry_levels.update(tp_levels)
            
            self._log_success(symbol, f"Turtle Soup + IFVG giriş hesaplandı: "
                            f"Entry={self.format_price(entry_price)}, "
                            f"SL={self.format_price(stop_loss)}, "
                            f"Confluence={confluence_score:.1f}")
            
            return entry_levels
            
        except Exception as e:
            self._log_error(symbol, f"Turtle Soup + IFVG giriş hesaplama hatası: {e}", exc_info=True)
            return {"primary_entry": None, "strategy_used": "turtle_soup_ifvg_error"}
    
    def _calculate_turtle_soup_ifvg_optimal_entry(self, turtle_soup_data: Dict[str, Any], 
                                                ifvg_data: Dict[str, Any],
                                                current_price: float, 
                                                trade_direction: str) -> Optional[float]:
        """
        Turtle Soup + IFVG için optimal entry seviyesini hesaplar.
        """
        try:
            # Turtle Soup breakout seviyesi
            turtle_breakout_price = turtle_soup_data.get('breakout_price', 0)
            
            # IFVG inversion seviyesi
            ifvg_inversion_price = ifvg_data.get('inversion_point', 0)
            
            if not turtle_breakout_price or not ifvg_inversion_price:
                return None
            
            # Optimal entry: İki seviye arasındaki orta nokta
            optimal_entry = (turtle_breakout_price + ifvg_inversion_price) / 2
            
            # Entry'nin current price'a yakınlığını kontrol et
            distance_pct = abs(optimal_entry - current_price) / current_price * 100
            
            if distance_pct > 2.0:  # %2'den uzaksa current price kullan
                optimal_entry = current_price
            
            return optimal_entry
            
        except Exception as e:
            logger.error(f"Turtle Soup + IFVG optimal entry hesaplama hatası: {e}")
            return None
    
    def _calculate_turtle_soup_ifvg_stop_loss(self, turtle_soup_data: Dict[str, Any],
                                            ifvg_data: Dict[str, Any],
                                            entry_price: float,
                                            trade_direction: str) -> Optional[float]:
        """
        Turtle Soup + IFVG için stop loss seviyesini hesaplar.
        """
        try:
            # Turtle Soup invalidation seviyesi
            if trade_direction.upper() == 'BULLISH':
                # Bullish Turtle Soup: Period low altında SL
                period_low = turtle_soup_data.get('period_low', 0)
                if period_low > 0:
                    stop_loss = period_low * 0.999  # %0.1 buffer
                    return stop_loss
            else:
                # Bearish Turtle Soup: Period high üstünde SL
                period_high = turtle_soup_data.get('period_high', 0)
                if period_high > 0:
                    stop_loss = period_high * 1.001  # %0.1 buffer
                    return stop_loss
            
            # Fallback: %2 SL
            if trade_direction.upper() == 'BULLISH':
                return entry_price * 0.98
            else:
                return entry_price * 1.02
                
        except Exception as e:
            logger.error(f"Turtle Soup + IFVG SL hesaplama hatası: {e}")
            return None
    
    def _calculate_turtle_soup_ifvg_take_profits(self, turtle_soup_data: Dict[str, Any],
                                               ifvg_data: Dict[str, Any],
                                               entry_price: float,
                                               stop_loss: float,
                                               trade_direction: str) -> Dict[str, float]:
        """
        Turtle Soup + IFVG için take profit seviyelerini hesaplar.
        """
        try:
            if not entry_price or not stop_loss:
                return {}
            
            # Risk hesapla
            risk = abs(entry_price - stop_loss)
            
            # TP seviyeleri (R:R bazlı) - stats_tracker.py ile uyumlu
            tp_levels = {}

            if trade_direction.upper() == 'BULLISH':
                tp_levels['tp1'] = entry_price + (risk * 1.0)    # 1.0R
                tp_levels['tp1_5'] = entry_price + (risk * 1.5)  # 1.5R
                tp_levels['tp2'] = entry_price + (risk * 2.5)    # 2.5R
                tp_levels['tp3'] = entry_price + (risk * 4.0)    # 4R
            else:
                tp_levels['tp1'] = entry_price - (risk * 1.0)    # 1.0R
                tp_levels['tp1_5'] = entry_price - (risk * 1.5)  # 1.5R
                tp_levels['tp2'] = entry_price - (risk * 2.5)    # 2.5R
                tp_levels['tp3'] = entry_price - (risk * 4.0)    # 4R
            
            return tp_levels
            
        except Exception as e:
            logger.error(f"Turtle Soup + IFVG TP hesaplama hatası: {e}")
            return {}
    
    def _fallback_bos_entry(self, symbol: str, stats: Dict[str, Any], trade_direction: str,
                           pattern_details: Dict[str, Any], order_blocks: Optional[Dict[str, Any]],
                           fvg_data: Optional[List[Dict[str, Any]]], 
                           swing_points: Optional[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Hiyerarşik strateji başarısız olduğunda kullanılan temel OTE yaklaşımı."""
        try:
            logger.info(f"[{symbol}] Fallback BOS giriş stratejisi devreye giriyor...")
            
            break_data = pattern_details.get('break_data', {})
            leg_start_price = break_data.get('leg_start_price')
            leg_end_price = break_data.get('leg_end_price')
            
            # Alternatif leg bilgilerini dene
            if not leg_start_price or not leg_end_price:
                leg_start_price = pattern_details.get('leg_start_price') or pattern_details.get('broken_pivot_price')
                leg_end_price = pattern_details.get('leg_end_price') or pattern_details.get('price')
            
            if not leg_start_price or not leg_end_price:
                logger.error(f"[{symbol}] Fallback BOS için leg bilgileri bulunamadı")
                return {"primary_entry": None, "strategy_used": "fallback_failed"}
            
            range_high = max(leg_start_price, leg_end_price)
            range_low = min(leg_start_price, leg_end_price)
            range_size = range_high - range_low
            
            if trade_direction.lower() == 'bullish':
                sweet_spot = range_high - (range_size * 0.705)
            else:
                sweet_spot = range_low + (range_size * 0.705)
            
            entry_levels = {"primary_entry": sweet_spot, "strategy_used": "fallback_ote_sweet_spot"}
            sl_tp_levels = self._calculate_sl_tp(sweet_spot, trade_direction, swing_points, 'IMPULSIVE')
            entry_levels.update(sl_tp_levels)
            
            logger.info(f"[{symbol}] Fallback strateji tamamlandı: sweet_spot @ {sweet_spot:.4f}")
            return entry_levels
            
        except Exception as e:
            logger.error(f"[{symbol}] Fallback BOS giriş stratejisi hatası: {e}", exc_info=True)
            return {"primary_entry": None, "strategy_used": "fallback_error"}

    def _calculate_order_block_entry(self, symbol: str, order_blocks: Dict[str, Any], last_price: float, trade_direction: str, fibonacci_data: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Order block'ları ve potansiyel Fibonacci birleşimlerini kullanarak giriş hesaplar."""
        ob_to_use = None
        entry_price = None
        strategy = "order_block"

        if trade_direction.upper() == "BULL":
            bullish_ob_list = order_blocks.get('bullish')
            if bullish_ob_list:
                bullish_ob = bullish_ob_list[0]
                if bullish_ob and bullish_ob.get('high') < last_price:
                    ob_to_use = bullish_ob
                    entry_price = ob_to_use.get('top', ob_to_use.get('high'))
                    strategy = "order_block_bullish"
        elif trade_direction.upper() == "BEAR":
            bearish_ob_list = order_blocks.get('bearish')
            if bearish_ob_list:
                bearish_ob = bearish_ob_list[0]
                if bearish_ob and bearish_ob.get('low') > last_price:
                    ob_to_use = bearish_ob
                    entry_price = ob_to_use.get('bottom', ob_to_use.get('low'))
                    strategy = "order_block_bearish"

        if not ob_to_use or not entry_price:
            return None

        fib_confluence_level = None
        float_levels = self._get_float_levels(fibonacci_data)
        if float_levels:
            for level, data in float_levels.items():
                fib_price = data['price']
                if ob_to_use.get('low') <= fib_price <= ob_to_use.get('high'):
                    entry_price = (entry_price + fib_price) / 2
                    fib_confluence_level = str(level)
                    strategy += "_fib_confluence"
                    logger.info(f"[{symbol}] OB/Fib kesişimi bulundu! Seviye: {fib_confluence_level}, Yeni Giriş: {entry_price:.4f}")
                    break

        return {"primary_entry": entry_price, "strategy_used": strategy, "fib_level": fib_confluence_level}

    def _calculate_sl_tp(self, entry_price: float, trade_direction: str,
                         swing_points: Optional[List[Dict[str, Any]]],
                         regime: str,
                         trigger_pivot_index: Optional[int] = None,
                         liquidity_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Piyasa rejimine göre dinamik Stop-Loss ve Take-Profit hesaplamalarını yapar.
        YENİ: Liquidity Analyzer'dan gelen BSL/SSL verilerini kullanarak dinamik TP hedeflemesi.
        """
        sl_tp = {}
        if not entry_price: return sl_tp

        final_sl = None
        structural_sl_price = None
        structural_pivot = None
        
        # Stop Loss hesaplama (mevcut mantık korundu)
        if swing_points:
            min_distance = entry_price * self.min_pivot_distance_pct
            if trade_direction.upper() == "BULL":
                protective_lows = [
                    p for p in swing_points
                    if (p.get('type') == 'low' or p.get('ict_type') in ['LL', 'HL'])
                    and p.get('price') < entry_price
                    and (entry_price - p.get('price')) >= min_distance
                    and (trigger_pivot_index is None or p.get('index', float('inf')) < trigger_pivot_index)
                ]
                if protective_lows:
                    structural_pivot = max(protective_lows, key=lambda x: x['price'])
                    structural_sl_price = structural_pivot['price'] * (1 - self.structure_sl_buffer_pct)
            elif trade_direction.upper() == "BEAR":
                protective_highs = [
                    p for p in swing_points
                    if (p.get('type') == 'high' or p.get('ict_type') in ['HH', 'LH'])
                    and p.get('price') > entry_price
                    and (p.get('price') - entry_price) >= min_distance
                    and (trigger_pivot_index is None or p.get('index', float('inf')) < trigger_pivot_index)
                ]
                if protective_highs:
                    structural_pivot = min(protective_highs, key=lambda x: x['price'])
                    structural_sl_price = structural_pivot['price'] * (1 + self.structure_sl_buffer_pct)

        if structural_sl_price:
            risk_pct = abs(entry_price - structural_sl_price) / entry_price
            max_risk = self.structural_sl_max_risk_corrective_pct if regime == 'CORRECTIVE' else self.structural_sl_max_risk_impulsive_pct
            if self.structural_sl_min_risk_pct <= risk_pct <= max_risk:
                final_sl = structural_sl_price
                sl_tp["sl_type"] = f'structural_{regime.lower()}'
            else:
                final_sl = None
        
        if final_sl is None:
            final_sl = entry_price * (1 - self.default_sl_pct if trade_direction.upper() == "BULL" else 1 + self.default_sl_pct)
            sl_tp["sl_type"] = 'default_percentage'
        
        sl_tp["stop_loss"] = final_sl
        risk_amount = abs(entry_price - final_sl)

        # YENİ: Dinamik TP hesaplama - Liquidity verilerini kullan
        dynamic_tp_result = self._calculate_dynamic_tp_levels(
            entry_price, trade_direction, risk_amount, liquidity_data
        )
        
        # Eğer dinamik TP bulunamazsa, geleneksel RR oranlarını kullan
        if not dynamic_tp_result.get('tp2') and not dynamic_tp_result.get('tp3'):
            if trade_direction.upper() == "BULL":
                sl_tp["tp1"] = entry_price + (risk_amount * self.tp1_rr_ratio)
                sl_tp["tp1_5"] = entry_price + (risk_amount * self.tp1_5_rr_ratio)
                sl_tp["tp2"] = entry_price + (risk_amount * self.tp2_rr_ratio)
                sl_tp["tp3"] = entry_price + (risk_amount * self.tp3_rr_ratio)
            else:
                sl_tp["tp1"] = entry_price - (risk_amount * self.tp1_rr_ratio)
                sl_tp["tp1_5"] = entry_price - (risk_amount * self.tp1_5_rr_ratio)
                sl_tp["tp2"] = entry_price - (risk_amount * self.tp2_rr_ratio)
                sl_tp["tp3"] = entry_price - (risk_amount * self.tp3_rr_ratio)
            sl_tp["tp_strategy"] = "traditional_rr"
        else:
            # Dinamik TP seviyelerini kullan
            sl_tp.update(dynamic_tp_result)
            sl_tp["tp_strategy"] = "dynamic_liquidity"
            
        sl_tp["sl_percentage"] = (risk_amount / entry_price) * 100 if entry_price != 0 else 0
        if sl_tp.get("tp1"):
            tp1_profit_dollars = abs(sl_tp["tp1"] - entry_price)
            sl_tp["tp_percentage"] = (tp1_profit_dollars / entry_price) * 100 if entry_price != 0 else 0

        return sl_tp

    def _calculate_dynamic_tp_levels(self, entry_price: float, trade_direction: str, 
                                   risk_amount: float, liquidity_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Liquidity Analyzer'dan gelen BSL/SSL verilerini kullanarak dinamik TP seviyeleri hesaplar.
        
        ICT Mantığı:
        - Bullish sinyaller için: En yakın ve mantıklı BSL seviyelerini TP2/TP3 olarak kullan
        - Bearish sinyaller için: En yakın ve mantıklı SSL seviyelerini TP2/TP3 olarak kullan
        
        Args:
            entry_price: Giriş fiyatı
            trade_direction: İşlem yönü (BULL/BEAR)
            risk_amount: Risk miktarı (SL mesafesi)
            liquidity_data: Liquidity analyzer'dan gelen veriler
            
        Returns:
            Dinamik TP seviyeleri
        """
        tp_levels = {}
        
        # Geleneksel TP1 ve TP1.5'i her zaman hesapla
        if trade_direction.upper() == "BULL":
            tp_levels["tp1"] = entry_price + (risk_amount * self.tp1_rr_ratio)
            tp_levels["tp1_5"] = entry_price + (risk_amount * self.tp1_5_rr_ratio)
        else:
            tp_levels["tp1"] = entry_price - (risk_amount * self.tp1_rr_ratio)
            tp_levels["tp1_5"] = entry_price - (risk_amount * self.tp1_5_rr_ratio)
        
        # Liquidity verilerini kontrol et
        if not liquidity_data:
            logger.debug("Liquidity verisi yok, geleneksel TP hesaplanacak")
            return tp_levels
        
        # External liquidity verilerini al
        external_liquidity = liquidity_data.get('external_liquidity', {})
        if not external_liquidity:
            logger.debug("External liquidity verisi yok")
            return tp_levels
        
        try:
            if trade_direction.upper() == "BULL":
                # Bullish sinyal için BSL (Buy Side Liquidity) seviyelerini hedefle
                bsl_zones = external_liquidity.get('bsl_zones', [])
                suitable_bsl = self._find_suitable_liquidity_targets(
                    entry_price, bsl_zones, 'BSL', trade_direction
                )
                
                if suitable_bsl:
                    tp_levels.update(self._assign_liquidity_tp_levels(
                        entry_price, suitable_bsl, trade_direction, risk_amount
                    ))
                    logger.debug(f"Bullish sinyal için {len(suitable_bsl)} BSL hedefi bulundu")
                
            elif trade_direction.upper() == "BEAR":
                # Bearish sinyal için SSL (Sell Side Liquidity) seviyelerini hedefle
                ssl_zones = external_liquidity.get('ssl_zones', [])
                suitable_ssl = self._find_suitable_liquidity_targets(
                    entry_price, ssl_zones, 'SSL', trade_direction
                )
                
                if suitable_ssl:
                    tp_levels.update(self._assign_liquidity_tp_levels(
                        entry_price, suitable_ssl, trade_direction, risk_amount
                    ))
                    logger.debug(f"Bearish sinyal için {len(suitable_ssl)} SSL hedefi bulundu")
                    
        except Exception as e:
            logger.error(f"Dinamik TP hesaplama hatası: {e}", exc_info=True)
        
        return tp_levels
    
    def _find_suitable_liquidity_targets(self, entry_price: float, liquidity_zones: List[Dict[str, Any]], 
                                       zone_type: str, trade_direction: str) -> List[Dict[str, Any]]:
        """
        Giriş fiyatına göre uygun likidite hedeflerini filtreler.
        
        Args:
            entry_price: Giriş fiyatı
            liquidity_zones: BSL veya SSL zone'ları
            zone_type: 'BSL' veya 'SSL'
            trade_direction: İşlem yönü
            
        Returns:
            Uygun likidite hedefleri (score'a göre sıralı)
        """
        suitable_targets = []
        
        for zone in liquidity_zones:
            zone_price = zone.get('price', 0)
            zone_score = zone.get('score', 0)
            
            # Temel filtreler
            if zone_price <= 0 or zone_score < 3:  # Minimum kalite eşiği
                continue
            
            # Yön kontrolü
            if trade_direction.upper() == "BULL":
                # Bullish için BSL hedefleri entry'nin üstünde olmalı
                if zone_type == 'BSL' and zone_price > entry_price:
                    distance_pct = (zone_price - entry_price) / entry_price * 100
                    # Çok yakın (<%0.5) veya çok uzak (>%10) hedefleri filtrele
                    if 0.5 <= distance_pct <= 10.0:
                        zone_copy = zone.copy()
                        zone_copy['distance_pct'] = distance_pct
                        suitable_targets.append(zone_copy)
                        
            elif trade_direction.upper() == "BEAR":
                # Bearish için SSL hedefleri entry'nin altında olmalı
                if zone_type == 'SSL' and zone_price < entry_price:
                    distance_pct = (entry_price - zone_price) / entry_price * 100
                    # Çok yakın (<%0.5) veya çok uzak (>%10) hedefleri filtrele
                    if 0.5 <= distance_pct <= 10.0:
                        zone_copy = zone.copy()
                        zone_copy['distance_pct'] = distance_pct
                        suitable_targets.append(zone_copy)
        
        # Score ve mesafeye göre sırala (yüksek score, yakın mesafe öncelikli)
        suitable_targets.sort(key=lambda x: (x.get('score', 0), -x.get('distance_pct', 999)), reverse=True)
        
        # En fazla 3 hedef döndür
        return suitable_targets[:3]
    
    def _assign_liquidity_tp_levels(self, entry_price: float, liquidity_targets: List[Dict[str, Any]], 
                                  trade_direction: str, risk_amount: float) -> Dict[str, Any]:
        """
        Bulunan likidite hedeflerini TP2 ve TP3 seviyelerine atar.
        
        Args:
            entry_price: Giriş fiyatı
            liquidity_targets: Uygun likidite hedefleri
            trade_direction: İşlem yönü
            risk_amount: Risk miktarı
            
        Returns:
            TP2 ve TP3 seviyeleri
        """
        tp_assignments = {}
        
        if not liquidity_targets:
            return tp_assignments
        
        # İlk hedefi TP2 olarak ata
        if len(liquidity_targets) >= 1:
            first_target = liquidity_targets[0]
            tp2_price = first_target['price']
            
            # Minimum RR kontrolü (en az 1.5:1 olmalı)
            expected_profit = abs(tp2_price - entry_price)
            rr_ratio = expected_profit / risk_amount if risk_amount > 0 else 0
            
            if rr_ratio >= 1.5:
                tp_assignments["tp2"] = tp2_price
                tp_assignments["tp2_liquidity_info"] = {
                    'zone_type': first_target.get('type', 'UNKNOWN'),
                    'zone_score': first_target.get('score', 0),
                    'distance_pct': first_target.get('distance_pct', 0),
                    'rr_ratio': rr_ratio
                }
                logger.debug(f"TP2 likidite hedefi: {tp2_price} (RR: {rr_ratio:.2f})")
        
        # İkinci hedefi TP3 olarak ata
        if len(liquidity_targets) >= 2:
            second_target = liquidity_targets[1]
            tp3_price = second_target['price']
            
            # Minimum RR kontrolü (en az 2.0:1 olmalı)
            expected_profit = abs(tp3_price - entry_price)
            rr_ratio = expected_profit / risk_amount if risk_amount > 0 else 0
            
            if rr_ratio >= 2.0:
                tp_assignments["tp3"] = tp3_price
                tp_assignments["tp3_liquidity_info"] = {
                    'zone_type': second_target.get('type', 'UNKNOWN'),
                    'zone_score': second_target.get('score', 0),
                    'distance_pct': second_target.get('distance_pct', 0),
                    'rr_ratio': rr_ratio
                }
                logger.debug(f"TP3 likidite hedefi: {tp3_price} (RR: {rr_ratio:.2f})")
        
        # Eğer uygun likidite hedefi bulunamazsa, geleneksel RR'ları kullan
        if not tp_assignments.get("tp2"):
            if trade_direction.upper() == "BULL":
                tp_assignments["tp2"] = entry_price + (risk_amount * self.tp2_rr_ratio)
            else:
                tp_assignments["tp2"] = entry_price - (risk_amount * self.tp2_rr_ratio)
            tp_assignments["tp2_liquidity_info"] = {'fallback': 'traditional_rr'}
        
        if not tp_assignments.get("tp3"):
            if trade_direction.upper() == "BULL":
                tp_assignments["tp3"] = entry_price + (risk_amount * self.tp3_rr_ratio)
            else:
                tp_assignments["tp3"] = entry_price - (risk_amount * self.tp3_rr_ratio)
            tp_assignments["tp3_liquidity_info"] = {'fallback': 'traditional_rr'}
        
        return tp_assignments

    def _log_fibonacci_levels(self, symbol: str, fib_levels: Dict[str, Any]) -> None:
        """Fibonacci seviyelerini loglar"""
        try:
            logger.debug(f"[{symbol}] 📊 Fibonacci Seviyeleri:")
            for level_key, level_price in sorted(fib_levels.items()):
                if isinstance(level_price, (int, float)):
                    logger.debug(f"[{symbol}]   {level_key}: {level_price}")
        except Exception as e:
            self._log_error(symbol, f"Fibonacci seviye loglama hatası: {e}")

    def _calculate_monday_range_entry(self, symbol: str, stats: Dict[str, Any],
                                    trade_direction: str, pattern_details: Dict[str, Any],
                                    swing_points: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Monday Range Reversal sinyali için özel giriş seviyesi hesaplama.

        Monday Range Reversal stratejisi:
        - Giriş: Monday Range EQ (Equilibrium) seviyesi
        - SL: Deviation yönüne göre Monday High/Low + buffer
        - TP: Risk/Reward oranına göre hesaplanır

        Args:
            symbol: Symbol adı
            stats: Symbol istatistikleri (last_price içerir)
            trade_direction: İşlem yönü ('bull'/'bear')
            pattern_details: Monday Range verilerini içeren pattern detayları
            swing_points: Swing point verileri (SL hesaplama için)

        Returns:
            Dict: Giriş seviyeleri ve strateji bilgileri
        """
        try:
            current_price = stats.get("last_price")
            if not current_price:
                self._log_error(symbol, "Monday Range entry için güncel fiyat bulunamadı")
                return {"primary_entry": None, "strategy_used": "monday_range_no_price"}

            # Pattern details'den Monday Range verilerini al
            monday_range_data = pattern_details.get('pattern_details', {})
            monday_range = monday_range_data.get('monday_range', [None, None])
            monday_range_eq = monday_range_data.get('monday_range_eq')
            deviation_direction = monday_range_data.get('deviation_direction')
            price_in_range_at_signal = monday_range_data.get('price_in_range_at_signal', True)  # Default True for backward compatibility

            if not monday_range or monday_range == [None, None] or not monday_range_eq:
                self._log_error(symbol, "Monday Range entry için gerekli veriler eksik")
                return {"primary_entry": None, "strategy_used": "monday_range_incomplete_data"}

            # ✅ YENİ KONTROL: Sinyal üretildiği anda fiyat range dışındaysa giriş yapma
            if not price_in_range_at_signal:
                self._log_warning(symbol, f"Monday Range Entry: Sinyal üretildiği anda fiyat range dışındaydı, giriş geçersiz")
                return {"primary_entry": None, "strategy_used": "monday_range_invalid_signal"}

            monday_low, monday_high = monday_range

            self._log_info(symbol, f"Monday Range Entry - EQ: {monday_range_eq:.6f}, Range: {monday_low:.6f}-{monday_high:.6f}")

            # ✅ DÜZELTME: Mevcut fiyat kontrolü ve fallback stratejisi
            distance_to_eq_pct = abs(monday_range_eq - current_price) / current_price * 100

            if distance_to_eq_pct > 5.0:  # %5'ten uzaksa
                self._log_warning(symbol, f"Monday Range EQ ({monday_range_eq:.6f}) mevcut fiyattan çok uzak (%{distance_to_eq_pct:.2f})")

                # Fallback: Mevcut fiyata yakın bir seviye kullan
                if trade_direction.lower() in ['bull', 'bullish']:
                    # Bullish için: Mevcut fiyat ile Monday Low arasında
                    if current_price < monday_low:
                        entry_price = monday_low * 1.002  # Monday Low'un %0.2 üstü
                        self._log_info(symbol, f"Fallback: Monday Low üstü giriş ({entry_price:.6f})")
                    else:
                        entry_price = current_price * 1.001  # Mevcut fiyatın %0.1 üstü
                        self._log_info(symbol, f"Fallback: Mevcut fiyat üstü giriş ({entry_price:.6f})")
                else:
                    # Bearish için: Mevcut fiyat ile Monday High arasında
                    if current_price > monday_high:
                        entry_price = monday_high * 0.998  # Monday High'ın %0.2 altı
                        self._log_info(symbol, f"Fallback: Monday High altı giriş ({entry_price:.6f})")
                    else:
                        entry_price = current_price * 0.999  # Mevcut fiyatın %0.1 altı
                        self._log_info(symbol, f"Fallback: Mevcut fiyat altı giriş ({entry_price:.6f})")
            else:
                # Normal durum: Monday Range EQ seviyesi
                entry_price = monday_range_eq

            # Stop Loss hesaplama: Deviation yönüne göre
            if trade_direction.lower() in ['bull', 'bullish']:
                # Bullish trade: SL Monday Low'un altında
                stop_loss = monday_low * (1 - self.structure_sl_buffer_pct)  # %0.5 buffer
                self._log_info(symbol, f"Bullish Monday Range - SL: Monday Low ({monday_low:.6f}) - %{self.structure_sl_buffer_pct*100:.1f} buffer")
            else:
                # Bearish trade: SL Monday High'ın üstünde
                stop_loss = monday_high * (1 + self.structure_sl_buffer_pct)  # %0.5 buffer
                self._log_info(symbol, f"Bearish Monday Range - SL: Monday High ({monday_high:.6f}) + %{self.structure_sl_buffer_pct*100:.1f} buffer")

            # Risk miktarını hesapla
            risk_amount = abs(entry_price - stop_loss)

            # Take Profit hesaplama (2:1 R:R oranı)
            if trade_direction.lower() in ['bull', 'bullish']:
                take_profit = entry_price + (risk_amount * 2.0)
            else:
                take_profit = entry_price - (risk_amount * 2.0)

            # Risk kontrolü
            risk_pct = risk_amount / entry_price
            if risk_pct > 0.05:  # %5'ten fazla risk
                self._log_warning(symbol, f"Monday Range risk çok yüksek: %{risk_pct*100:.2f}")
                # Risk çok yüksekse fallback SL kullan
                if trade_direction.lower() in ['bull', 'bullish']:
                    stop_loss = entry_price * 0.975  # %2.5 SL
                else:
                    stop_loss = entry_price * 1.025  # %2.5 SL

            # --- DÜZELTME BAŞLANGICI ---
            # _calculate_sl_tp fonksiyonunu çağırmak yerine, burada hesaplanan SL'i kullan
            # ve TP'leri bu SL'e göre hesapla.
            risk_amount_final = abs(entry_price - stop_loss)

            # TP seviyelerini Monday Range SL'e göre hesapla
            if trade_direction.lower() in ['bull', 'bullish']:
                tp1 = entry_price + (risk_amount_final * self.tp1_rr_ratio)
                tp2 = entry_price + (risk_amount_final * self.tp2_rr_ratio)
                tp3 = entry_price + (risk_amount_final * self.tp3_rr_ratio)
            else:
                tp1 = entry_price - (risk_amount_final * self.tp1_rr_ratio)
                tp2 = entry_price - (risk_amount_final * self.tp2_rr_ratio)
                tp3 = entry_price - (risk_amount_final * self.tp3_rr_ratio)

            entry_levels = {
                "primary_entry": entry_price,
                "stop_loss": stop_loss,
                "tp1": tp1,
                "tp2": tp2,
                "tp3": tp3,
                "strategy_used": "monday_range_reversal_corrected",
                "sl_type": "monday_range_structural",
                "entry_logic": f"Monday Range EQ @ {monday_range_eq:.6f}, Deviation: {deviation_direction}",
                "sl_percentage": abs((entry_price - stop_loss) / entry_price) * 100,
                "tp_percentage": abs((tp1 - entry_price) / entry_price) * 100
            }
            # --- DÜZELTME SONU ---

            self._log_info(symbol, f"✅ Monday Range Entry (DÜZELTME) - Entry: {entry_price:.6f}, SL: {entry_levels.get('stop_loss'):.6f} (Monday Range Structural), TP1: {entry_levels.get('tp1'):.6f}")

            return entry_levels

        except Exception as e:
            self._log_error(symbol, f"Monday Range entry hesaplama hatası: {e}")
            return {"primary_entry": None, "strategy_used": "monday_range_error"}

    def _calculate_liqsfp_reversal_entry(self, symbol: str, stats: Dict[str, Any],
                                       trade_direction: str, pattern_details: Dict[str, Any],
                                       candles: Optional[pd.DataFrame] = None, **kwargs) -> Dict[str, Any]:
        last_price = stats.get("last_price")
        entry_levels = {"primary_entry": None, "strategy_used": "liqsfp_reversal"}
        
        if not last_price or not pattern_details:
            return entry_levels
            
        hunt_price = pattern_details.get('hunt_price') or pattern_details.get('swing_price')
        if not hunt_price:
            return entry_levels
            
        safe_distance = hunt_price * 0.001
        
        if trade_direction.lower() in ['bull', 'long']:
            entry_price = hunt_price + safe_distance
        else:
            entry_price = hunt_price - safe_distance
            
        entry_levels["primary_entry"] = entry_price
        
        sl_tp_levels = self._calculate_sl_tp(
            entry_price=entry_price,
            trade_direction=trade_direction,
            swing_points=kwargs.get('swing_points'),
            regime='REVERSAL',
            liquidity_data=kwargs.get('liquidity_data')
        )
        entry_levels.update(sl_tp_levels)
        return entry_levels

    def _calculate_htf_poi_ltf_mss_entry(self, symbol: str, stats: Dict[str, Any],
                                        trade_direction: str, pattern_details: Dict[str, Any],
                                        candles: Optional[pd.DataFrame] = None, **kwargs) -> Dict[str, Any]:
        """HTF POI + LTF MSS için giriş hesaplar."""
        logger.info(f"[{symbol}] HTF POI + LTF MSS giriş stratejisi...")
        current_price = stats.get("last_price")
        if not current_price: return {"primary_entry": None, "strategy_used": "htf_poi_ltf_mss_failed"}
        
        htf_poi_data = pattern_details.get('htf_poi_data', {})
        ltf_mss_data = pattern_details.get('ltf_mss_data', {})
        if not htf_poi_data or not ltf_mss_data:
            return {"primary_entry": None, "strategy_used": "htf_poi_ltf_mss_incomplete"}
        
        entry_calculation = self._calculate_htf_poi_entry_levels(htf_poi_data, ltf_mss_data, current_price, trade_direction.upper())
        if not entry_calculation.get('primary_entry'):
            return {"primary_entry": None, "strategy_used": "htf_poi_ltf_mss_no_entry"}
        
        sl_tp_levels = self._calculate_htf_poi_sl_tp(
            entry_calculation['primary_entry'], trade_direction.upper(), htf_poi_data, ltf_mss_data
        )
        
        result = {"strategy_used": "htf_poi_ltf_mss"}
        result.update(entry_calculation)
        result.update(sl_tp_levels)
        return result

    def _calculate_liquidity_hunt_weak_strong_entry(self, symbol: str, stats: Dict[str, Any],
                                                   trade_direction: str, pattern_details: Dict[str, Any],
                                                   candles: Optional[pd.DataFrame] = None, **kwargs) -> Dict[str, Any]:
        """Likidite Avı + Zayıf/Güçlü Swing için giriş hesaplar."""
        logger.info(f"[{symbol}] Likidite Avı + Zayıf/Güçlü Swing giriş stratejisi...")
        current_price = stats.get("last_price")
        if not current_price: return {"primary_entry": None, "strategy_used": "liq_hunt_weak_strong_failed"}

        liquidity_hunt_data = pattern_details.get('liquidity_hunt_data', {})
        weak_strong_data = pattern_details.get('weak_strong_data', {})
        
        if not liquidity_hunt_data or not weak_strong_data:
            return {"primary_entry": None, "strategy_used": "liq_hunt_weak_strong_incomplete"}
            
        entry_calculation = self._calculate_liq_hunt_entry_levels(
            liquidity_hunt_data, weak_strong_data, current_price, trade_direction.upper()
        )
        if not entry_calculation.get('primary_entry'):
            return {"primary_entry": None, "strategy_used": "liq_hunt_weak_strong_no_entry"}

        sl_tp_levels = self._calculate_liq_hunt_sl_tp(
            entry_calculation['primary_entry'], trade_direction.upper(), weak_strong_data, liquidity_hunt_data
        )
        
        result = {"strategy_used": "liquidity_hunt_weak_strong"}
        result.update(entry_calculation)
        result.update(sl_tp_levels)
        return result

    def _calculate_htf_poi_entry_levels(self, htf_poi_data, ltf_mss_data, current_price, trade_direction):
        """HTF POI + LTF MSS için optimum giriş seviyelerini hesaplar."""
        ltf_entry_zones = ltf_mss_data.get('entry_zones', [])
        primary_entry = None
        if ltf_entry_zones:
            best_ltf_zone = self._select_best_ltf_entry_zone(ltf_entry_zones, current_price, trade_direction)
            if best_ltf_zone:
                primary_entry = best_ltf_zone.get('optimal_price')
        
        if not primary_entry:
            htf_optimal_levels = self._calculate_htf_poi_optimal_levels(htf_poi_data, trade_direction)
            if htf_optimal_levels:
                primary_entry = htf_optimal_levels[0]
        
        return {'primary_entry': primary_entry}
        
    def _calculate_htf_poi_sl_tp(self, entry_price, trade_direction, htf_poi_data, ltf_mss_data):
        """HTF POI + LTF MSS için SL/TP seviyelerini hesaplar."""
        stop_loss = self._calculate_htf_poi_stop_loss(entry_price, trade_direction, htf_poi_data, ltf_mss_data)
        risk_amount = abs(entry_price - stop_loss)
        
        # TP seviyelerini hesapla
        if trade_direction == 'BULLISH':
            tp1 = entry_price + (risk_amount * self.tp1_rr_ratio)
            tp1_5 = entry_price + (risk_amount * self.tp1_5_rr_ratio)
            tp2 = entry_price + (risk_amount * self.tp2_rr_ratio)
            tp3 = entry_price + (risk_amount * self.tp3_rr_ratio)
        else:
            tp1 = entry_price - (risk_amount * self.tp1_rr_ratio)
            tp1_5 = entry_price - (risk_amount * self.tp1_5_rr_ratio)
            tp2 = entry_price - (risk_amount * self.tp2_rr_ratio)
            tp3 = entry_price - (risk_amount * self.tp3_rr_ratio)
        
        # Risk/Reward oranını hesapla
        tp1_profit = abs(tp1 - entry_price)
        risk_reward_ratio = tp1_profit / risk_amount if risk_amount > 0 else 0
        
        return {
            'stop_loss': stop_loss,
            'tp1': tp1,
            'tp1_5': tp1_5,
            'tp2': tp2,
            'tp3': tp3,
            'risk_reward': risk_reward_ratio,
            'risk_reward_ratio': risk_reward_ratio,
            'sl_percentage': (risk_amount / entry_price) * 100 if entry_price != 0 else 0,
            'tp_percentage': (tp1_profit / entry_price) * 100 if entry_price != 0 else 0
        }

    def _calculate_htf_poi_stop_loss(self, entry_price, trade_direction, htf_poi_data, ltf_mss_data):
        """HTF POI için optimum stop loss seviyesini hesaplar."""
        poi_invalidation = htf_poi_data.get('invalidation_level')
        ltf_swing_level = ltf_mss_data.get('swing_invalidation_level')
        poi_edge = htf_poi_data.get('bottom') if trade_direction == 'BULLISH' else htf_poi_data.get('top')
        
        candidates = [c for c in [poi_invalidation, ltf_swing_level, poi_edge] if c]
        if not candidates:
            return entry_price * (1 - 0.02) if trade_direction == 'BULLISH' else entry_price * (1 + 0.02)
        
        return min(candidates) if trade_direction == 'BULLISH' else max(candidates)

    def _calculate_htf_poi_optimal_levels(self, htf_poi_data, trade_direction):
        """HTF POI içindeki optimal seviyeleri hesaplar."""
        poi_type = htf_poi_data.get('poi_type')
        top = htf_poi_data.get('top', 0)
        bottom = htf_poi_data.get('bottom', 0)
        if top <= bottom: return []
        
        range_size = top - bottom
        if poi_type == 'fvg':
            if trade_direction == 'BULLISH':
                return [bottom + range_size * r for r in [0.21, 0.50, 0.38]]
            else:
                return [top - range_size * r for r in [0.21, 0.50, 0.38]]
        elif poi_type == 'order_block':
            if trade_direction == 'BULLISH':
                return [bottom + range_size * r for r in [0.15, 0.50, 0.85]]
            else:
                return [top - range_size * r for r in [0.15, 0.50, 0.85]]
        return []

    def _calculate_liq_hunt_entry_levels(self, liquidity_hunt_data, weak_strong_data, current_price, trade_direction):
        """Likidite avı için giriş seviyelerini hesaplar."""
        hunt_price = liquidity_hunt_data.get('hunt_price', current_price)
        pullback_entry = self._calculate_hunt_pullback_entry(hunt_price, trade_direction, liquidity_hunt_data)
        return {'primary_entry': pullback_entry}
    
    def _calculate_liq_hunt_sl_tp(self, entry_price, trade_direction, weak_strong_data, liquidity_hunt_data):
        """Likidite avı için SL/TP hesaplar."""
        reference_swing = weak_strong_data.get('reference_swing', {})
        stop_loss = self._calculate_liq_hunt_stop_loss(entry_price, trade_direction, reference_swing)
        risk_amount = abs(entry_price - stop_loss)
        
        # TP seviyelerini hesapla
        if trade_direction == 'BULLISH':
            tp1 = entry_price + (risk_amount * self.tp1_rr_ratio)
            tp1_5 = entry_price + (risk_amount * self.tp1_5_rr_ratio)
            tp2 = entry_price + (risk_amount * self.tp2_rr_ratio)
            tp3 = entry_price + (risk_amount * self.tp3_rr_ratio)
        else:
            tp1 = entry_price - (risk_amount * self.tp1_rr_ratio)
            tp1_5 = entry_price - (risk_amount * self.tp1_5_rr_ratio)
            tp2 = entry_price - (risk_amount * self.tp2_rr_ratio)
            tp3 = entry_price - (risk_amount * self.tp3_rr_ratio)
        
        # Risk/Reward oranını hesapla
        tp1_profit = abs(tp1 - entry_price)
        risk_reward_ratio = tp1_profit / risk_amount if risk_amount > 0 else 0
        
        return {
            'stop_loss': stop_loss,
            'tp1': tp1,
            'tp1_5': tp1_5,
            'tp2': tp2,
            'tp3': tp3,
            'risk_reward': risk_reward_ratio,
            'risk_reward_ratio': risk_reward_ratio,
            'sl_percentage': (risk_amount / entry_price) * 100 if entry_price != 0 else 0,
            'tp_percentage': (tp1_profit / entry_price) * 100 if entry_price != 0 else 0
        }
    
    def _calculate_liq_hunt_stop_loss(self, entry_price, trade_direction, reference_swing):
        """Likidite avı için stop loss hesaplar."""
        swing_price = reference_swing.get('price', entry_price)
        swing_type = reference_swing.get('type')
        buffer_pct = 0.002
        
        if trade_direction == 'BULLISH':
            return swing_price * (1 - buffer_pct) if swing_type == 'low' else entry_price * (1 - 0.02)
        else:
            return swing_price * (1 + buffer_pct) if swing_type == 'high' else entry_price * (1 + 0.02)

    def _select_best_ltf_entry_zone(self, ltf_zones, current_price, trade_direction):
        """LTF entry zoneları arasından en iyisini seçer."""
        if not ltf_zones: return None
        for zone in ltf_zones:
            zone['distance'] = abs(zone['optimal_price'] - current_price) / current_price
        
        return min(ltf_zones, key=lambda x: x['distance'])

    def _calculate_hunt_pullback_entry(self, hunt_price, trade_direction, liquidity_hunt_data):
        """Likidite avı sonrası pullback girişini hesaplar."""
        strength = liquidity_hunt_data.get('strength', 5)
        completion = liquidity_hunt_data.get('completion_percentage', 50)
        adjusted_pullback_pct = min(0.015, 0.003 * (1 + strength / 10 + completion / 100))
        return hunt_price * (1 - adjusted_pullback_pct) if trade_direction == 'BULLISH' else hunt_price * (1 + adjusted_pullback_pct)

    def _calculate_killzone_session_entry(self, symbol: str, stats: Dict[str, Any],
                                         trade_direction: str, pattern_details: Dict[str, Any],
                                         swing_points: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Killzone Session Manipulation için giriş hesaplar."""
        logger.info(f"[{symbol}] Killzone Session Manipulation giriş stratejisi...")
        current_price = stats.get("last_price")
        if not current_price: 
            return {"primary_entry": None, "strategy_used": "killzone_session_failed"}

        # Manipulation price'ı al
        manipulation_data = pattern_details.get('manipulation_data', {})
        manipulation_price = manipulation_data.get('manipulation_price', current_price)
        
        # Safe distance ekle
        safe_distance = manipulation_price * 0.002  # %0.2
        
        if trade_direction.lower() in ['bull', 'bullish']:
            entry_price = manipulation_price + safe_distance
        else:
            entry_price = manipulation_price - safe_distance
            
        entry_levels = {
            "primary_entry": entry_price,
            "strategy_used": "killzone_session_manipulation"
        }
        
        sl_tp_levels = self._calculate_sl_tp(
            entry_price=entry_price,
            trade_direction=trade_direction,
            swing_points=swing_points,
            regime='SESSION_MANIPULATION'
        )
        entry_levels.update(sl_tp_levels)
        return entry_levels

    def _calculate_equal_highs_lows_entry(self, symbol: str, stats: Dict[str, Any],
                                        trade_direction: str, pattern_details: Dict[str, Any],
                                        swing_points: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Equal Highs/Lows breakout için giriş hesaplar."""
        logger.info(f"[{symbol}] Equal Highs/Lows breakout giriş stratejisi...")
        current_price = stats.get("last_price")
        if not current_price: 
            return {"primary_entry": None, "strategy_used": "eqh_eql_failed"}

        # Equal level price'ı al
        equal_level_data = pattern_details.get('equal_level_data', {})
        equal_price = equal_level_data.get('level_price', current_price)
        
        # Breakout confirmation distance
        confirmation_distance = equal_price * 0.0015  # %0.15
        
        if trade_direction.lower() in ['bull', 'bullish']:
            entry_price = equal_price + confirmation_distance
        else:
            entry_price = equal_price - confirmation_distance
            
        entry_levels = {
            "primary_entry": entry_price,
            "strategy_used": "equal_highs_lows_breakout"
        }
        
        sl_tp_levels = self._calculate_sl_tp(
            entry_price=entry_price,
            trade_direction=trade_direction,
            swing_points=swing_points,
            regime='BREAKOUT'
        )
        entry_levels.update(sl_tp_levels)
        return entry_levels

    def _calculate_volume_imbalance_entry(self, symbol: str, stats: Dict[str, Any],
                                        trade_direction: str, pattern_details: Dict[str, Any],
                                        swing_points: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Volume Imbalance confluence için giriş hesaplar."""
        logger.info(f"[{symbol}] Volume Imbalance confluence giriş stratejisi...")
        current_price = stats.get("last_price")
        if not current_price: 
            return {"primary_entry": None, "strategy_used": "vi_confluence_failed"}

        # VI confluence data'sını al
        vi_data = pattern_details.get('vi_data', {})
        vi_price = vi_data.get('confluence_price', current_price)
        
        # Quality-based entry adjustment
        quality_score = vi_data.get('quality_score', 5)
        quality_multiplier = min(1.5, quality_score / 10)  # Max 1.5x
        
        base_distance = vi_price * 0.001  # %0.1 base
        adjusted_distance = base_distance * quality_multiplier
        
        if trade_direction.lower() in ['bull', 'bullish']:
            entry_price = vi_price + adjusted_distance
        else:
            entry_price = vi_price - adjusted_distance
            
        entry_levels = {
            "primary_entry": entry_price,
            "strategy_used": "volume_imbalance_confluence"
        }
        
        sl_tp_levels = self._calculate_sl_tp(
            entry_price=entry_price,
            trade_direction=trade_direction,
            swing_points=swing_points,
            regime='IMBALANCE'
        )
        entry_levels.update(sl_tp_levels)
        return entry_levels

    def _calculate_rejection_block_entry(self, symbol: str, stats: Dict[str, Any],
                                       trade_direction: str, pattern_details: Dict[str, Any],
                                       swing_points: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Rejection Block için giriş hesaplar."""
        logger.info(f"[{symbol}] Rejection Block giriş stratejisi...")
        current_price = stats.get("last_price")
        if not current_price: 
            return {"primary_entry": None, "strategy_used": "rejection_block_failed"}

        # Rejection block data'sını al
        rb_data = pattern_details.get('rejection_data', {})
        block_top = rb_data.get('block_top', current_price)
        block_bottom = rb_data.get('block_bottom', current_price)
        
        # Block'un orta noktasından giriş
        block_mid = (block_top + block_bottom) / 2
        
        # Direction-based fine tuning
        if trade_direction.lower() in ['bull', 'bullish']:
            entry_price = block_bottom + ((block_mid - block_bottom) * 0.25)  # Alt %25'lik kısım
        else:
            entry_price = block_top - ((block_top - block_mid) * 0.25)  # Üst %25'lik kısım
            
        entry_levels = {
            "primary_entry": entry_price,
            "strategy_used": "rejection_block"
        }
        
        sl_tp_levels = self._calculate_sl_tp(
            entry_price=entry_price,
            trade_direction=trade_direction,
            swing_points=swing_points,
            regime='REJECTION'
        )
        entry_levels.update(sl_tp_levels)
        return entry_levels
    
    def _calculate_turtle_soup_ifvg_entry(self, symbol: str, stats: Dict[str, Any],
                                         trade_direction: str, pattern_details: Dict[str, Any],
                                         swing_points: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Turtle Soup + IFVG confluence sinyalleri için giriş hesaplar.
        
        Turtle Soup + IFVG Stratejisi:
        1. False breakout (turtle soup) tespit edilir
        2. IFVG (Inverse Fair Value Gap) confluence kontrol edilir
        3. Reversal direction'da optimal entry hesaplanır
        4. Structural SL ve TP seviyeleri belirlenir
        
        Args:
            symbol: Sembol adı
            stats: Fiyat bilgileri
            trade_direction: İşlem yönü
            pattern_details: Turtle Soup + IFVG pattern detayları
            swing_points: Swing noktaları (SL hesaplama için)
            
        Returns:
            Dict: Giriş seviyeleri ve strateji bilgileri
        """
        try:
            current_price = stats.get("last_price")
            if not current_price:
                return {"primary_entry": None, "strategy_used": "turtle_soup_ifvg_failed"}
            
            self._log_info(symbol, f"Turtle Soup + IFVG giriş hesaplaması - Yön: {trade_direction}")
            
            # Turtle Soup ve IFVG verilerini al
            turtle_soup_data = pattern_details.get('turtle_soup_data', {})
            ifvg_data = pattern_details.get('ifvg_data', {})
            confluence_score = pattern_details.get('confluence_score', 0)
            
            if not turtle_soup_data or not ifvg_data:
                self._log_warning(symbol, "Turtle Soup veya IFVG verisi eksik")
                return {"primary_entry": None, "strategy_used": "turtle_soup_ifvg_incomplete"}
            
            # Entry seviyesini hesapla
            entry_price = self._calculate_turtle_soup_entry_price(
                turtle_soup_data, ifvg_data, current_price, trade_direction
            )
            
            if not entry_price:
                self._log_warning(symbol, "Turtle Soup + IFVG için optimal entry hesaplanamadı")
                return {"primary_entry": None, "strategy_used": "turtle_soup_ifvg_no_entry"}
            
            # Stop Loss hesapla (structural)
            stop_loss = self._calculate_turtle_soup_stop_loss(
                turtle_soup_data, ifvg_data, entry_price, trade_direction, swing_points
            )
            
            # Take Profit seviyeleri hesapla
            tp_levels = self._calculate_turtle_soup_take_profits(
                entry_price, stop_loss, trade_direction, turtle_soup_data
            )
            
            # Risk/Reward hesapla
            risk_amount = abs(entry_price - stop_loss) if stop_loss else 0
            tp1_profit = abs(tp_levels.get('tp1', entry_price) - entry_price) if tp_levels.get('tp1') else 0
            risk_reward_ratio = (tp1_profit / risk_amount) if risk_amount > 0 else 0
            
            result = {
                'primary_entry': entry_price,
                'strategy_used': 'turtle_soup_ifvg_confluence',
                'stop_loss': stop_loss,
                'tp1': tp_levels.get('tp1'),
                'tp1_5': tp_levels.get('tp1_5'),
                'tp2': tp_levels.get('tp2'),
                'tp3': tp_levels.get('tp3'),
                'confluence_score': confluence_score,
                'turtle_soup_strength': turtle_soup_data.get('strength', 0),
                'ifvg_strength': ifvg_data.get('inversion_strength', 0),
                'risk_reward': risk_reward_ratio,
                'risk_reward_ratio': risk_reward_ratio,
                'sl_percentage': (risk_amount / entry_price) * 100 if entry_price != 0 else 0,
                'tp_percentage': (tp1_profit / entry_price) * 100 if entry_price != 0 else 0,
                'entry_method': 'turtle_soup_ifvg',
                'quality_rating': self._get_turtle_soup_quality_rating(confluence_score)
            }
            
            self._log_success(symbol, f"Turtle Soup + IFVG giriş hesaplandı: "
                            f"Entry={self.format_price(entry_price)}, "
                            f"SL={self.format_price(stop_loss)}, "
                            f"TP1={self.format_price(tp_levels.get('tp1'))}, "
                            f"R:R={risk_reward_ratio:.2f}, "
                            f"Confluence={confluence_score:.1f}")
            
            return result
            
        except Exception as e:
            self._log_error(symbol, f"Turtle Soup + IFVG giriş hesaplama hatası: {e}", exc_info=True)
            return {"primary_entry": None, "strategy_used": "turtle_soup_ifvg_error"}
    
    def _calculate_turtle_soup_entry_price(self, turtle_soup_data: Dict[str, Any],
                                          ifvg_data: Dict[str, Any], 
                                          current_price: float,
                                          trade_direction: str) -> Optional[float]:
        """
        Turtle Soup + IFVG için optimal entry fiyatını hesaplar.
        """
        try:
            # Turtle Soup seviyesi (false breakout seviyesi)
            soup_level = turtle_soup_data.get('ssl_level') or turtle_soup_data.get('bsl_level', 0)
            
            # IFVG inversion point
            ifvg_level = ifvg_data.get('inversion_point', 0)
            
            if soup_level == 0 or ifvg_level == 0:
                return None
            
            # Entry seviyesini confluence noktası olarak hesapla
            if trade_direction.upper() in ['BULL', 'BULLISH']:
                # Bullish: Soup level üstünde, IFVG yakınında
                entry_price = max(soup_level, ifvg_level) * 1.001  # %0.1 üstünde
            else:
                # Bearish: Soup level altında, IFVG yakınında  
                entry_price = min(soup_level, ifvg_level) * 0.999  # %0.1 altında
            
            # Current price'dan çok uzak olmamalı (%2 limit)
            distance_pct = abs(entry_price - current_price) / current_price * 100
            if distance_pct > 2.0:
                # Çok uzaksa current price'a yaklaştır
                if trade_direction.upper() in ['BULL', 'BULLISH']:
                    entry_price = current_price * 1.005  # %0.5 üstünde
                else:
                    entry_price = current_price * 0.995  # %0.5 altında
            
            return entry_price
            
        except Exception as e:
            logger.error(f"Turtle Soup entry price hesaplama hatası: {e}", exc_info=True)
            return None
    
    def _calculate_turtle_soup_stop_loss(self, turtle_soup_data: Dict[str, Any],
                                        ifvg_data: Dict[str, Any],
                                        entry_price: float, trade_direction: str,
                                        swing_points: Optional[List[Dict[str, Any]]]) -> Optional[float]:
        """
        Turtle Soup + IFVG için structural stop loss hesaplar.
        """
        try:
            if trade_direction.upper() in ['BULL', 'BULLISH']:
                # Bullish: Turtle soup seviyesinin altında SL
                soup_level = turtle_soup_data.get('ssl_level', 0)
                if soup_level > 0:
                    return soup_level * 0.998  # %0.2 altında güvenlik marjı
            else:
                # Bearish: Turtle soup seviyesinin üstünde SL
                soup_level = turtle_soup_data.get('bsl_level', 0)
                if soup_level > 0:
                    return soup_level * 1.002  # %0.2 üstünde güvenlik marjı
            
            # Fallback: Swing-based SL
            if swing_points:
                return self._calculate_swing_based_sl(entry_price, trade_direction, swing_points)
            
            # Son çare: %2 SL
            if trade_direction.upper() in ['BULL', 'BULLISH']:
                return entry_price * 0.98
            else:
                return entry_price * 1.02
                
        except Exception as e:
            logger.error(f"Turtle Soup SL hesaplama hatası: {e}", exc_info=True)
            return None
    
    def _calculate_turtle_soup_take_profits(self, entry_price: float, stop_loss: float,
                                           trade_direction: str, 
                                           turtle_soup_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Turtle Soup + IFVG için take profit seviyeleri hesaplar.
        """
        try:
            if not stop_loss:
                return {}
            
            risk_amount = abs(entry_price - stop_loss)
            
            # Turtle Soup strength'e göre TP multiplier'ları ayarla
            soup_strength = turtle_soup_data.get('strength', 5)
            
            if soup_strength >= 8:
                # Çok güçlü turtle soup - agresif TP'ler
                tp1_multiplier = 2.0
                tp2_multiplier = 3.5
                tp3_multiplier = 5.0
            elif soup_strength >= 6:
                # Orta güçlü turtle soup - standart TP'ler
                tp1_multiplier = 1.5
                tp2_multiplier = 2.5
                tp3_multiplier = 4.0
            else:
                # Zayıf turtle soup - konservatif TP'ler
                tp1_multiplier = 1.0
                tp2_multiplier = 2.0
                tp3_multiplier = 3.0
            
            if trade_direction.upper() in ['BULL', 'BULLISH']:
                tp1 = entry_price + (risk_amount * tp1_multiplier)
                tp1_5 = entry_price + (risk_amount * (tp1_multiplier + 0.5))
                tp2 = entry_price + (risk_amount * tp2_multiplier)
                tp3 = entry_price + (risk_amount * tp3_multiplier)
            else:
                tp1 = entry_price - (risk_amount * tp1_multiplier)
                tp1_5 = entry_price - (risk_amount * (tp1_multiplier + 0.5))
                tp2 = entry_price - (risk_amount * tp2_multiplier)
                tp3 = entry_price - (risk_amount * tp3_multiplier)
            
            return {
                'tp1': tp1,
                'tp1_5': tp1_5,
                'tp2': tp2,
                'tp3': tp3
            }
            
        except Exception as e:
            logger.error(f"Turtle Soup TP hesaplama hatası: {e}", exc_info=True)
            return {}
    
    def _get_turtle_soup_quality_rating(self, confluence_score: float) -> str:
        """
        Turtle Soup + IFVG confluence skoruna göre kalite rating'i döndürür.
        """
        if confluence_score >= 90:
            return 'EXCELLENT'
        elif confluence_score >= 80:
            return 'VERY_GOOD'
        elif confluence_score >= 70:
            return 'GOOD'
        elif confluence_score >= 60:
            return 'ACCEPTABLE'
        else:
            return 'POOR'
    
    def format_price(self, price: Optional[float]) -> str:
        """Fiyatı formatlar"""
        if price is None:
            return "None"
        try:
            return f"{price:.4f}"
        except (TypeError, ValueError):
            return str(price)

    def _log_fibonacci_levels(self, symbol: str, fib_levels: Dict[str, Any]) -> None:
        """Fibonacci seviyelerini loglar - debug amaçlı"""
        try:
            if not fib_levels:
                return
            
            key_levels = ['0.382', '0.5', '0.618', '0.705', '0.786']
            level_info = []
            
            for level in key_levels:
                if level in fib_levels:
                    level_info.append(f"{level}: {fib_levels[level]:.4f}")
            
            if level_info:
                self._log_debug(symbol, f"Fibonacci seviyeleri: {', '.join(level_info)}")
                
        except Exception as e:
            self._log_error(symbol, f"Fibonacci seviye loglama hatası: {e}")



    def _calculate_breaker_block_entry(self, symbol: str, stats: Dict[str, Any],
                                     trade_direction: str, pattern_details: Dict[str, Any],
                                     swing_points: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Breaker Block Retest sinyalleri için giriş seviyesi hesaplar.
        
        Breaker Block'lar, başarısız olan bir hareketin öncesindeki son zıt yönlü mumun
        oluşturduğu POI bölgeleridir. Bu bölgelerin yeniden test edilmesi yüksek olasılıklı
        giriş fırsatları sunar.
        
        Args:
            symbol: Ticaret sembolü
            stats: Fiyat bilgileri
            trade_direction: İşlem yönü ('bull' veya 'bear')
            pattern_details: Breaker retest sinyal detayları
            swing_points: Swing noktaları (SL hesaplama için)
            
        Returns:
            Dict: Giriş, SL ve TP seviyeleri
        """
        try:
            self._log_info(symbol, "🔄 Breaker Block Retest giriş stratejisi başlatılıyor...")
            
            last_price = stats.get("last_price", 0)
            if not last_price:
                self._log_error(symbol, "Breaker Block entry için fiyat bilgisi eksik")
                return {"primary_entry": None, "strategy_used": "breaker_retest_error"}
            
            # Pattern details'den POI bilgilerini al
            poi_top = pattern_details.get('poi_top')
            poi_bottom = pattern_details.get('poi_bottom')
            confidence = pattern_details.get('confidence', 0.8)
            
            if not poi_top or not poi_bottom:
                self._log_error(symbol, "Breaker Block POI sınırları bulunamadı")
                return {"primary_entry": None, "strategy_used": "breaker_retest_missing_poi"}
            
            # Giriş stratejisi: POI bölgesinin kenarından giriş
            if trade_direction.lower() in ['bull', 'bullish']:
                # Bullish Breaker: POI'nin alt sınırından (destek) giriş
                primary_entry = poi_bottom
                entry_logic = "Bullish Breaker Block - POI alt sınırı (destek)"
            else:
                # Bearish Breaker: POI'nin üst sınırından (direnç) giriş  
                primary_entry = poi_top
                entry_logic = "Bearish Breaker Block - POI üst sınırı (direnç)"
            
            # Girişin mevcut fiyata yakınlığını kontrol et
            distance_pct = abs(primary_entry - last_price) / last_price
            
            # Eğer giriş çok uzaksa, mevcut fiyattan gir (fallback)
            if distance_pct > self.max_entry_distance_pct:
                self._log_warning(symbol, f"Breaker Block girişi çok uzak (%{distance_pct*100:.2f}), "
                                         f"market price kullanılıyor")
                primary_entry = last_price
                entry_logic += " (Market Price Fallback)"
            
            # Stop Loss hesaplama: POI'nin karşı tarafı + buffer
            if trade_direction.lower() in ['bull', 'bullish']:
                # Bullish için SL: POI üstünün biraz altı
                stop_loss = poi_top * (1 - self.structure_sl_buffer_pct)
            else:
                # Bearish için SL: POI altının biraz üstü
                stop_loss = poi_bottom * (1 + self.structure_sl_buffer_pct)
            
            # Risk kontrolü
            risk_pct = abs(primary_entry - stop_loss) / primary_entry
            if risk_pct > self.structural_sl_max_risk_impulsive_pct:
                self._log_warning(symbol, f"Breaker Block riski yüksek (%{risk_pct*100:.2f}), "
                                         f"varsayılan SL kullanılıyor")
                # Varsayılan SL kullan
                if trade_direction.lower() in ['bull', 'bullish']:
                    stop_loss = primary_entry * (1 - self.default_sl_pct)
                else:
                    stop_loss = primary_entry * (1 + self.default_sl_pct)
                risk_pct = self.default_sl_pct
            
            # Take Profit seviyeleri - Confidence'a göre ayarla
            risk_amount = abs(primary_entry - stop_loss)
            
            # Yüksek confidence'lı Breaker'lar için agresif TP'ler
            if confidence >= 0.85:
                tp_multipliers = {'tp1': 1.5, 'tp1_5': 2.0, 'tp2': 2.5, 'tp3': 3.5}
            else:
                tp_multipliers = {'tp1': 1.0, 'tp1_5': 1.5, 'tp2': 2.0, 'tp3': 3.0}
            
            if trade_direction.lower() in ['bull', 'bullish']:
                tp1 = primary_entry + (risk_amount * tp_multipliers['tp1'])
                tp1_5 = primary_entry + (risk_amount * tp_multipliers['tp1_5'])
                tp2 = primary_entry + (risk_amount * tp_multipliers['tp2'])
                tp3 = primary_entry + (risk_amount * tp_multipliers['tp3'])
            else:
                tp1 = primary_entry - (risk_amount * tp_multipliers['tp1'])
                tp1_5 = primary_entry - (risk_amount * tp_multipliers['tp1_5'])
                tp2 = primary_entry - (risk_amount * tp_multipliers['tp2'])
                tp3 = primary_entry - (risk_amount * tp_multipliers['tp3'])
            
            entry_levels = {
                'primary_entry': primary_entry,
                'stop_loss': stop_loss,
                'tp1': tp1,
                'tp1_5': tp1_5,
                'tp2': tp2,
                'tp3': tp3,
                'strategy_used': 'breaker_block_retest',
                'entry_logic': entry_logic,
                'risk_reward_ratio': tp_multipliers['tp1'],
                'risk_percentage': risk_pct * 100,
                'confidence_level': confidence,
                'poi_range': {'top': poi_top, 'bottom': poi_bottom},
                'quality_rating': 'EXCELLENT' if confidence >= 0.9 else 'VERY_GOOD' if confidence >= 0.8 else 'GOOD'
            }
            
            self._log_success(symbol, f"✅ Breaker Block Retest girişi hesaplandı: "
                                    f"Entry={self.format_price(primary_entry)}, "
                                    f"SL={self.format_price(stop_loss)}, "
                                    f"Risk=%{risk_pct*100:.2f}")
            
            return entry_levels
            
        except Exception as e:
            self._log_error(symbol, f"Breaker Block entry hesaplama hatası: {e}", exc_info=True)
            return {"primary_entry": None, "strategy_used": "breaker_retest_error"}

    def _calculate_breaker_block_retest_entry(self, symbol: str, stats: Dict[str, Any],
                                            trade_direction: str, pattern_details: Dict[str, Any],
                                            swing_points: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Gerçek Breaker Block Retest sinyali için özel giriş seviyesi hesaplama.
        breaker_block_analyzer.analyze_retest metodundan gelen verilerle çalışır.

        Args:
            symbol: Symbol adı
            stats: Symbol istatistikleri (last_price içerir)
            trade_direction: İşlem yönü ('bull'/'bear')
            pattern_details: Breaker Block retest verilerini içeren pattern detayları
            swing_points: Swing point verileri (SL hesaplama için)

        Returns:
            Dict: Giriş seviyeleri ve strateji bilgileri
        """
        try:
            last_price = stats.get("last_price", 0)
            if not last_price:
                self._log_error(symbol, "Breaker Block Retest entry için fiyat bilgisi eksik")
                return {"primary_entry": None, "strategy_used": "breaker_block_retest_error"}

            # Pattern details'den Breaker Block bilgilerini al
            breaker_block = pattern_details.get('details', {})
            retest_price = pattern_details.get('price', last_price)

            if not breaker_block:
                self._log_error(symbol, "Breaker Block Retest için block bilgileri bulunamadı")
                return {"primary_entry": None, "strategy_used": "breaker_block_retest_missing_data"}

            block_top = breaker_block.get('top')
            block_bottom = breaker_block.get('bottom')

            if not block_top or not block_bottom:
                self._log_error(symbol, "Breaker Block sınırları bulunamadı")
                return {"primary_entry": None, "strategy_used": "breaker_block_retest_missing_bounds"}

            # Giriş stratejisi: Retest fiyatına yakın güvenli seviye
            if trade_direction.lower() in ['bull', 'bullish']:
                # Bullish için: Block bottom'un biraz üstü
                entry_price = max(retest_price, block_bottom * 1.001)  # %0.1 buffer
                stop_loss = block_bottom * 0.995  # Block bottom'un %0.5 altı
            else:
                # Bearish için: Block top'un biraz altı
                entry_price = min(retest_price, block_top * 0.999)  # %0.1 buffer
                stop_loss = block_top * 1.005  # Block top'un %0.5 üstü

            self._log_info(symbol, f"Breaker Block Retest Entry - Entry: {entry_price:.6f}, Block: {block_bottom:.6f}-{block_top:.6f}")

            # Risk kontrolü
            risk_amount = abs(entry_price - stop_loss)
            risk_pct = risk_amount / entry_price

            if risk_pct > 0.05:  # %5'ten fazla risk
                self._log_warning(symbol, f"Breaker Block Retest risk çok yüksek: %{risk_pct*100:.2f}")
                # Risk çok yüksekse fallback SL kullan
                if trade_direction.lower() in ['bull', 'bullish']:
                    stop_loss = entry_price * 0.975  # %2.5 SL
                else:
                    stop_loss = entry_price * 1.025  # %2.5 SL

            # TP seviyelerini hesapla
            risk_amount_final = abs(entry_price - stop_loss)

            if trade_direction.lower() in ['bull', 'bullish']:
                tp1 = entry_price + (risk_amount_final * self.tp1_rr_ratio)
                tp2 = entry_price + (risk_amount_final * self.tp2_rr_ratio)
                tp3 = entry_price + (risk_amount_final * self.tp3_rr_ratio)
            else:
                tp1 = entry_price - (risk_amount_final * self.tp1_rr_ratio)
                tp2 = entry_price - (risk_amount_final * self.tp2_rr_ratio)
                tp3 = entry_price - (risk_amount_final * self.tp3_rr_ratio)

            entry_levels = {
                "primary_entry": entry_price,
                "stop_loss": stop_loss,
                "tp1": tp1,
                "tp2": tp2,
                "tp3": tp3,
                "strategy_used": "breaker_block_retest",
                "sl_type": "breaker_block_structural",
                "entry_logic": f"Breaker Block Retest @ {retest_price:.6f}",
                "sl_percentage": abs((entry_price - stop_loss) / entry_price) * 100,
                "tp_percentage": abs((tp1 - entry_price) / entry_price) * 100
            }

            self._log_info(symbol, f"✅ Breaker Block Retest Entry - Entry: {entry_price:.6f}, SL: {stop_loss:.6f}, TP1: {tp1:.6f}")

            return entry_levels

        except Exception as e:
            self._log_error(symbol, f"Breaker Block Retest entry hesaplama hatası: {e}", exc_info=True)
            return {"primary_entry": None, "strategy_used": "breaker_block_retest_error"}