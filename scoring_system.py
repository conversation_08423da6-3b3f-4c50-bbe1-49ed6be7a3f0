"""
Puanlama sistemi dokümantasyonu scoring_documentation.md dosyasına taşınmıştır.
"""

import os
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
import pandas as pd
from loguru import logger
from utils import format_price_standard
from stats_tracker import StatsTracker
from premium_discount_analyzer import PremiumDiscountAnalyzer
from bybit_client import BybitClient
from ict_concepts_analyzer import ICTConceptsAnalyzer
from fibonacci_analyzer import FibonacciAnalyzer
from ote_confluence_analyzer import OTEConfluenceAnalyzer
from smart_entry_strategy import SmartEntryStrategy # Gelişmiş giriş stratejisi     
from utils import format_price_standard
# Logger formatını değiştir - Modül/fonksiyon/satır bilgisini gizle
logger = logger.bind(format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")

# Stratejileri izole etmek için yardımcı sınıfları tanımla
class ScoringSystem:
    """
    Farklı teknik analiz sonuçlarını birleştiren ve puanlama yapan sınıf.
    Confluence (Kesişim) stratejisine dayanarak, işlem fırsatlarını puanlar
    ve sıralar.
    """

    def __init__(self, stats_tracker: Optional[StatsTracker] = None,
                 smart_entry_strategy: Optional[SmartEntryStrategy] = None,
                 premium_discount_analyzer: Optional[PremiumDiscountAnalyzer] = None,
                 ict_concepts_analyzer: Optional[ICTConceptsAnalyzer] = None,
                 fibonacci_analyzer: Optional[FibonacciAnalyzer] = None,
                 ote_confluence_analyzer: Optional[OTEConfluenceAnalyzer] = None,
                 cvd_analyzer = None):
        """
        ScoringSystem sınıfını başlatır.

        Args:
            stats_tracker: İşlem istatistiklerini takip eden StatsTracker örneği
            smart_entry_strategy: Giriş stratejisi hesaplamaları için SmartEntryStrategy örneği
            premium_discount_analyzer: Premium/Discount analiz modülü (opsiyonel, DI için)
            ict_concepts_analyzer: ICT concepts analiz modülü (opsiyonel, DI için)
            fibonacci_analyzer: Fibonacci analiz modülü (opsiyonel, DI için)
            ote_confluence_analyzer: OTE Confluence analiz modülü (opsiyonel, DI için)
            cvd_analyzer: CVD analiz modülü (sentiment analizi için, opsiyonel, DI için)
        """
        self.scores: Dict[str, Dict[str, Any]] = {}
        self.main_timeframe: str = "240"
        self.other_timeframes: List[str] = ["D", "720", "60"]
        self.stats_tracker = stats_tracker

        # Dependency Injection: PremiumDiscountAnalyzer
        if premium_discount_analyzer is not None:
            self.pd_analyzer = premium_discount_analyzer
        else:
            self.pd_analyzer = PremiumDiscountAnalyzer()
            
        # Dependency Injection: ICTConceptsAnalyzer
        if ict_concepts_analyzer is not None:
            self.ict_analyzer = ict_concepts_analyzer
        else:
            self.ict_analyzer = ICTConceptsAnalyzer()

        # Dependency Injection: FibonacciAnalyzer (lazy initialization)
        if fibonacci_analyzer is not None:
            self.fibonacci_analyzer = fibonacci_analyzer
        else:
            self._fibonacci_analyzer = None
            
        # Dependency Injection: OTEConfluenceAnalyzer (lazy initialization)
        if ote_confluence_analyzer is not None:
            self.ote_confluence_analyzer = ote_confluence_analyzer
        else:
            self._ote_confluence_analyzer = None

        # Dependency Injection: CVDAnalyzer (sentiment analizi için)
        if cvd_analyzer is not None:
            self.cvd_analyzer = cvd_analyzer
        else:
            self.cvd_analyzer = None

        # Akıllı giriş stratejisi - Dependency Injection ile al, yoksa oluştur
        if smart_entry_strategy is not None:
            self.smart_entry_strategy = smart_entry_strategy
        else:
            # Backward compatibility için fallback
            from smart_entry_strategy import SmartEntryStrategy
            self.smart_entry_strategy = SmartEntryStrategy()

        logger.info("Puanlama sistemi başlatıldı - Gelişmiş dependency injection uygulandı")
    
    @property
    def fibonacci_analyzer(self):
        """Lazy initialization ile FibonacciAnalyzer'ı döndür"""
        if self._fibonacci_analyzer is None:
            from fibonacci_analyzer import FibonacciAnalyzer
            self._fibonacci_analyzer = FibonacciAnalyzer()
            logger.debug("ScoringSystem: FibonacciAnalyzer lazy initialization ile oluşturuldu")
        return self._fibonacci_analyzer
    
    @fibonacci_analyzer.setter
    def fibonacci_analyzer(self, value):
        """FibonacciAnalyzer setter"""
        self._fibonacci_analyzer = value
        
    @property
    def ote_confluence_analyzer(self):
        """Lazy initialization ile OTEConfluenceAnalyzer'ı döndür"""
        if self._ote_confluence_analyzer is None:
            from ote_confluence_analyzer import OTEConfluenceAnalyzer
            self._ote_confluence_analyzer = OTEConfluenceAnalyzer()
            logger.debug("ScoringSystem: OTEConfluenceAnalyzer lazy initialization ile oluşturuldu")
        return self._ote_confluence_analyzer
    
    @ote_confluence_analyzer.setter
    def ote_confluence_analyzer(self, value):
        """OTEConfluenceAnalyzer setter"""
        self._ote_confluence_analyzer = value

    def reset_scores(self):
        """
        Artık her döngüde puanları sıfırlamıyoruz.
        Puanlar, pattern koşulu değişene kadar korunacak.
        Bu fonksiyon, geriye dönük uyumluluk için korunmuştur.
        """
        # Puanları sıfırlamıyoruz, sadece log mesajı yazıyoruz
        logger.debug("Puanlama sistemi sıfırlama fonksiyonu çağrıldı, ancak puanlar korunuyor.")

    def format_price(self, price):
        """Fiyatı formatlar"""
        if price is None:
            return "None"
        try:
            return format_price_standard(price)
        except (TypeError, ValueError):
            logger.warning(f"Fiyat formatlanamadı: {price}")
            return str(price)

    def calculate_score(self, symbol: str, all_symbol_data: Dict[str, Any], main_signal: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Seçilmiş ana sinyali puanlar ve zenginleştirir.

        YENİ MİMARİ: Bu fonksiyon artık sinyal seçimi yapmaz, sadece verilen sinyali işler.
        SignalOrchestrator tarafından seçilen ana sinyal bu fonksiyona parametre olarak gelir.

        Args:
            symbol (str): Kripto para sembolü.
            all_symbol_data (Dict[str, Any]): O sembole ait tüm analiz verilerini içeren sözlük.
            main_signal (Optional[Dict[str, Any]]): SignalOrchestrator tarafından seçilen ana sinyal.
                                                   None ise eski davranış (geriye dönük uyumluluk).

        Returns:
            Optional[Dict[str, Any]]: Puanlama sonucunu içeren bir sözlük veya sinyal yoksa None.
        """
        # YENİ MİMARİ: Ana sinyal parametre olarak gelir
        if main_signal is not None:
            logger.info(f"[{symbol}] 🎯 SignalOrchestrator tarafından seçilen sinyal puanlanıyor: {main_signal.get('type')}")
        else:
            # ESKİ MİMARİ: Geriye dönük uyumluluk için (DEPRECATED)
            logger.warning(f"[{symbol}] ⚠️ DEPRECATED: calculate_score() eski modda çalışıyor. main_signal parametresi kullanılmalı.")
            potential_signals = self._determine_trade_signal(symbol, all_symbol_data)
            if not potential_signals:
                logger.info(f"[{symbol}] Puanlanacak potansiyel ticaret sinyali bulunamadı.")
                return None
            main_signal = potential_signals[0]
            logger.info(f"[{symbol}] Puanlama için seçilen sinyal: {main_signal.get('type')} (Toplam {len(potential_signals)} sinyal bulundu)")

        trade_direction = main_signal.get('direction')
        if not trade_direction:
            logger.warning(f"[{symbol}] Ana sinyalin yönü belirsiz, puanlama atlanıyor.")
            return None

        logger.info(f"[{symbol}] Ana Sinyal Bulundu: {main_signal.get('type')}, Yön: {trade_direction.upper()}")

        # 2. Puanlama Yapısını Başlat
        score_result = {
            "symbol": symbol,
            "trade_direction": trade_direction,
            "main_signal_type": main_signal.get('type'),
            "base_score": 5.0,  # Tüm geçerli sinyaller 5.0 taban puanı ile başlar
            "confirmation_score": 0.0,
            "negative_score": 0.0,
            "confirmation_details": [f"✅ Ana Sinyal: {main_signal.get('type')}"],
            "negative_details": [],
            "all_data": all_symbol_data # Raporlama için tüm veriyi sakla
        }

        # 3. Teyit ve Negatif Puanları Hesapla
        # Bu kısım, diğer modüllerden gelen verileri kullanarak ana sinyali güçlendirir veya zayıflatır.
        # Örneğin:
        # - HTF yönü ile uyum
        # - Fiyatın Premium/Discount bölgesinde olması
        # - Karşıt FVG/OB varlığı
        # - Supertrend, Divergence gibi ikincil göstergeler

        # Örnek: HTF Supertrend teyidi
        htf_supertrend = all_symbol_data.get('supertrend_D', {}).get('trend')
        if htf_supertrend:
            if (trade_direction == 'bull' and htf_supertrend == 'up') or \
               (trade_direction == 'bear' and htf_supertrend == 'down'):
                score_result["confirmation_score"] += 1.5
                score_result["confirmation_details"].append("✅ HTF Supertrend Teyidi")
            elif (trade_direction == 'bull' and htf_supertrend == 'down') or \
                 (trade_direction == 'bear' and htf_supertrend == 'up'):
                score_result["negative_score"] += 2.0
                score_result["negative_details"].append("❌ HTF Supertrend Uyumsuzluğu")

        # ... Diğer tüm teyit ve negatif puanlama mantıkları buraya eklenecek ...
        
        # YENİ: Gelişmiş ICT Konseptleri Confluence Puanlaması
        confluence_analysis = self._calculate_advanced_ict_confluence(symbol, all_symbol_data, trade_direction, main_signal)
        score_result['confirmation_score'] += confluence_analysis['positive_score']
        score_result['negative_score'] += confluence_analysis['negative_score']
        score_result['confirmation_details'].extend(confluence_analysis['positive_details'])
        score_result['negative_details'].extend(confluence_analysis['negative_details'])
        
        # Confluence skorunu ayrı olarak sakla (0-100 arası normalize et)
        total_confluence_score = confluence_analysis['positive_score'] - abs(confluence_analysis['negative_score'])
        normalized_confluence_score = max(0, min(100, total_confluence_score * 10))  # 0-10 arası skoru 0-100'e çevir
        score_result['confluence_score'] = normalized_confluence_score

        # 4. Net Skoru Hesapla
        net_score = score_result['base_score'] + score_result['confirmation_score'] + score_result['negative_score']
        score_result['net_score'] = net_score

        logger.success(f"[{symbol}] Puanlama Tamamlandı. Net Skor: {net_score:.2f}")
        
        # YENİ: Potansiyel kurulum analizi ve "beklemedeki sinyal" kontrolü
        potential_setup_analysis = self._analyze_potential_setup_quality(symbol, all_symbol_data, trade_direction, main_signal)
        score_result.update(potential_setup_analysis)

        # 5. Giriş/Çıkış Seviyelerini Belirle
        # SmartEntryStrategy, ana sinyal tipine ve diğer analiz verilerine göre seviyeleri belirler.
        entry_levels = self.smart_entry_strategy.calculate_entry_levels(
            symbol=symbol,
            stats={'last_price': all_symbol_data.get('candles', pd.DataFrame()).iloc[-1]['close'] if not all_symbol_data.get('candles', pd.DataFrame()).empty else 0},
            trade_direction=trade_direction,
            # --- DÜZELTME BAŞLANGICI ---
            # Premium/Discount ve BOS Fibonacci verilerini ayrı ayrı ve doğru anahtarlarla gönder
            premium_discount_data=all_symbol_data.get('fibonacci_analysis'),  # Premium/Discount için
            fibonacci_data=all_symbol_data.get('bos_fibonacci_analysis'),  # BOS için olan bu
            # --- DÜZELTME SONU ---
            order_blocks=all_symbol_data.get('main_tf_order_blocks'),
            swing_points=all_symbol_data.get('main_tf_structure', {}).get('major_pivots', []),
            candles=all_symbol_data.get('candles'),
            pattern_details=main_signal,
            # Stratejinin tüm verilere erişebilmesi için ekledim
            all_symbol_data=all_symbol_data
        )
        score_result.update(entry_levels)

        # --- YENİ EKLENECEK SATIR ---
        # Invalidation için pivot verisini nihai sinyale ekle - STANDARTLAŞTIRMA: major_pivots kullan
        score_result['major_pivots'] = all_symbol_data.get('main_tf_structure', {}).get('major_pivots', [])
        # --- YENİ SATIR SONU ---

        return score_result

    def _get_timeframe_label(self, timeframe: str) -> str:
        """
        Zaman diliminin görüntüleme etiketini döndürür.

        Args:
            timeframe (str): Zaman dilimi kodu (örn: 'D', '720', '240', '60')

        Returns:
            str: Görüntüleme etiketi (örn: 'D', '12h', '4h', '1h')
        """
        timeframe_labels = {
            'D': 'D',
            '720': '12h',
            '240': '4h',
            '60': '1h',
            '1d': 'D',
            '4h': '4h',
            '1h': '1h'
        }
        return timeframe_labels.get(timeframe, timeframe.upper())

    def _determine_direction_from_patterns(self, base_details: List[Tuple[str, str, float]]) -> Optional[str]:
        """
        Verilen base_details listesindeki pattern tiplerine göre işlem yönünü belirler.

        Args:
            base_details (List[Tuple[str, str, float]]): _score_patterns'dan dönen detay listesi.
                                                          Format: [("Bull PAT", "Pattern Name", Score), ...]

        Returns:
            Optional[str]: İşlem yönü ("bull", "bear" veya None)
        """
        if not base_details: # Eğer detay listesi boşsa None dön
             return None

        # Pattern tiplerini kontrol et
        has_bull_patterns = any(detail[0].startswith("Bull") for detail in base_details if isinstance(detail, tuple))
        has_bear_patterns = any(detail[0].startswith("Bear") for detail in base_details if isinstance(detail, tuple))

        # Yönü belirle
        if has_bull_patterns and not has_bear_patterns:
            return "bull"
        elif has_bear_patterns and not has_bull_patterns:
            return "bear"
        else: # Karışık veya bilinmeyen durum
            return None

    def _determine_trade_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Gelişmiş ICT stratejilerine göre tüm potansiyel ticaret sinyallerini belirler.
        "İlk bulan kazanır" mantığından "tüm adayları topla, en iyisini seç" mantığına geçiş.
        
        Bu fonksiyon artık ilk bulduğu sinyali döndürmek yerine, analiz sırasında 
        bulduğu tüm potansiyel sinyalleri bir liste olarak toplar ve döndürür.
        
        Returns:
            List[Dict[str, Any]]: Bulunan tüm potansiyel ticaret sinyalleri listesi
        """
        try:
            logger.info(f"[{symbol}] ICT tüm potansiyel sinyaller toplanıyor...")
            
            # Tüm potansiyel sinyalleri toplayacağımız liste
            potential_signals = []

            # SIFIRINCI ÖNCELİK: ICT 2022 Mentorship Modeli (Strateji bazlı en yüksek öncelik)
            mentorship_signals = all_symbol_data.get('mentorship_model_signals', [])
            if mentorship_signals:
                for signal in mentorship_signals:
                    if isinstance(signal, dict):
                        signal['priority_level'] = 0 # En yüksek öncelik
                        potential_signals.append(signal)
                        direction = signal.get('direction', 'UNKNOWN')
                        direction_str = direction.upper() if direction else 'UNKNOWN'
                        logger.info(f"[{symbol}] 🏆 ICT 2022 MODEL Sinyali eklendi: {direction_str}")
                    else:
                        logger.warning(f"[{symbol}] ⚠️ Mentorship sinyali geçersiz format: {type(signal)}")

            # BİRİNCİ ÖNCELİK: Silver Bullet (Zaman bazlı en yüksek öncelik)
            silver_bullet_signals = all_symbol_data.get('silver_bullet_analysis', [])
            if silver_bullet_signals:
                for sb_signal in silver_bullet_signals:
                    if isinstance(sb_signal, dict):
                        sb_signal['priority_level'] = 1  # Yüksek öncelik
                        potential_signals.append(sb_signal)
                        signal_type = sb_signal.get('type', 'UNKNOWN')
                        direction = sb_signal.get('direction', 'UNKNOWN')
                        logger.info(f"[{symbol}] 🏆 SILVER_BULLET Sinyali eklendi: {signal_type} ({direction})")
                    else:
                        logger.warning(f"[{symbol}] ⚠️ Silver Bullet sinyali geçersiz format: {type(sb_signal)}")

            # YENİ ÖNCELİK: AMD Modeli (Spring/UTAD)
            amd_signals = all_symbol_data.get('amd_analysis', [])
            if amd_signals:
                for amd_signal in amd_signals:
                    if isinstance(amd_signal, dict):
                        amd_signal['priority_level'] = 2
                        potential_signals.append(amd_signal)
                        signal_type = amd_signal.get('type', 'UNKNOWN')
                        direction = amd_signal.get('direction', 'UNKNOWN')
                        logger.info(f"[{symbol}] 🏆 AMD Modeli Sinyali eklendi: {signal_type} ({direction})")
                    else:
                        logger.warning(f"[{symbol}] ⚠️ AMD sinyali geçersiz format: {type(amd_signal)}")

            # YENİ ÖNCELİK: iFVG Retest (Inversion FVG'nin yeniden testi)
            ifvg_retest_signal = self._check_ifvg_retest_signal(symbol, all_symbol_data)
            if ifvg_retest_signal and isinstance(ifvg_retest_signal, dict):
                ifvg_retest_signal['priority_level'] = 3
                potential_signals.append(ifvg_retest_signal)
                direction = ifvg_retest_signal.get('direction', 'UNKNOWN')
                direction_str = direction.upper() if direction else 'UNKNOWN'
                logger.info(f"[{symbol}] 🏆 iFVG Retest Sinyali eklendi: {direction_str}")

            # ÖNCE: Killzone + Session Manipulation sinyallerini kontrol et (EN YÜKSEK ÖNCELİK)
            killzone_analysis = all_symbol_data.get('killzone_session_manipulation', {})
            if killzone_analysis and isinstance(killzone_analysis, dict):
                killzone_signals = killzone_analysis.get('signals', [])
                # ✅ DÜZELTME: Signals listesinin geçerli olduğundan emin ol
                if killzone_signals and isinstance(killzone_signals, list):
                    logger.info(f"[{symbol}] Killzone + Session Manipulation sinyali bulundu: {len(killzone_signals)} adet")
                    for kz_signal in killzone_signals:
                        if isinstance(kz_signal, dict) and kz_signal.get('type'):
                            kz_signal['priority_level'] = 4
                            potential_signals.append(kz_signal)
                            logger.info(f"[{symbol}] ✅ Killzone + Session Manipulation Sinyali eklendi: {kz_signal.get('type')} ({kz_signal.get('direction')})")
                        else:
                            logger.warning(f"[{symbol}] ⚠️ Killzone sinyali geçersiz format: {type(kz_signal)} - İçerik: {kz_signal}")
                elif killzone_signals:
                    logger.warning(f"[{symbol}] ⚠️ Killzone signals geçersiz tip: {type(killzone_signals)} - İçerik: {killzone_signals}")
                else:
                    logger.debug(f"[{symbol}] Killzone analizi mevcut ama sinyal bulunamadı")
            elif killzone_analysis:
                logger.warning(f"[{symbol}] ⚠️ Killzone analizi geçersiz format: {type(killzone_analysis)}")

            # İKİNCİ: HTF POI + LTF MSS sinyallerini kontrol et
            htf_poi_analysis = all_symbol_data.get('htf_poi_ltf_mss', {})
            if htf_poi_analysis and isinstance(htf_poi_analysis, dict):
                htf_poi_signals = htf_poi_analysis.get('signals', [])
                # ✅ DÜZELTME: Signals listesinin geçerli olduğundan emin ol
                if htf_poi_signals and isinstance(htf_poi_signals, list):
                    logger.info(f"[{symbol}] HTF POI + LTF MSS sinyali bulundu: {len(htf_poi_signals)} adet")
                    for poi_signal in htf_poi_signals:
                        if isinstance(poi_signal, dict) and poi_signal.get('type'):
                            poi_signal['priority_level'] = 5
                            potential_signals.append(poi_signal)
                            logger.info(f"[{symbol}] ✅ HTF POI + LTF MSS Sinyali eklendi: {poi_signal.get('type')} ({poi_signal.get('direction')})")
                        else:
                            logger.warning(f"[{symbol}] ⚠️ HTF POI sinyali geçersiz format: {type(poi_signal)} - İçerik: {poi_signal}")
                elif htf_poi_signals:
                    logger.warning(f"[{symbol}] ⚠️ HTF POI signals geçersiz tip: {type(htf_poi_signals)} - İçerik: {htf_poi_signals}")
                else:
                    logger.debug(f"[{symbol}] HTF POI analizi mevcut ama sinyal bulunamadı")
            elif htf_poi_analysis:
                logger.warning(f"[{symbol}] ⚠️ HTF POI analizi geçersiz format: {type(htf_poi_analysis)}")

            # ÜÇÜNCÜ: Likidite Avı + Zayıf/Güçlü Swing sinyallerini kontrol et
            liq_hunt_analysis = all_symbol_data.get('liquidity_hunt_weak_strong', {})
            if liq_hunt_analysis and isinstance(liq_hunt_analysis, dict):
                liq_hunt_signals = liq_hunt_analysis.get('signals', [])
                # ✅ DÜZELTME: Signals listesinin geçerli olduğundan emin ol
                if liq_hunt_signals and isinstance(liq_hunt_signals, list):
                    logger.info(f"[{symbol}] Likidite Avı + Swing sinyali bulundu: {len(liq_hunt_signals)} adet")
                    for hunt_signal in liq_hunt_signals:
                        if isinstance(hunt_signal, dict) and hunt_signal.get('type'):
                            hunt_signal['priority_level'] = 5
                            potential_signals.append(hunt_signal)
                            logger.info(f"[{symbol}] ✅ Likidite Avı + Swing Sinyali eklendi: {hunt_signal.get('type')} ({hunt_signal.get('direction')})")
                        else:
                            logger.warning(f"[{symbol}] ⚠️ Likidite Avı sinyali geçersiz format: {type(hunt_signal)} - İçerik: {hunt_signal}")
                elif liq_hunt_signals:
                    logger.warning(f"[{symbol}] ⚠️ Likidite Avı signals geçersiz tip: {type(liq_hunt_signals)} - İçerik: {liq_hunt_signals}")
                else:
                    logger.debug(f"[{symbol}] Likidite Avı analizi mevcut ama sinyal bulunamadı")
            elif liq_hunt_analysis:
                logger.warning(f"[{symbol}] ⚠️ Likidite Avı analizi geçersiz format: {type(liq_hunt_analysis)}")

            # YENİ: OB12_BOS1 sinyallerini confluence_aggregator'dan al
            ob12_bos1_signals = all_symbol_data.get('ob12_bos1_analysis', [])
            if ob12_bos1_signals:
                logger.info(f"[{symbol}] OB12_BOS1 sinyali bulundu: {len(ob12_bos1_signals)} adet")
                for signal in ob12_bos1_signals:
                    # Veri tipi kontrolü - signal bir dict olmalı
                    if isinstance(signal, dict):
                        potential_signals.append(signal)
                        logger.info(f"[{symbol}] ✅ OB12_BOS1 Sinyali eklendi: {signal.get('type')} ({signal.get('direction')})")
                    else:
                        logger.warning(f"[{symbol}] ⚠️ OB12_BOS1 sinyali geçersiz format: {type(signal)} - {signal}")
            
            # Gerekli analizleri al
            structure_analysis = all_symbol_data.get('main_tf_structure', {})
            liquidity_analysis = all_symbol_data.get('liquidity_analysis', {})
            # DÜZELTME: main_tf_order_blocks artık bir sözlük.
            order_block_analysis = all_symbol_data.get('main_tf_order_blocks', {})
            breaker_analysis = all_symbol_data.get('breaker_block_analysis', {})
            fvg_analysis = all_symbol_data.get('fvg_analysis', [])
            # DÜZELTME: external_liquidity, liquidity_analysis içindeki bir anahtar.
            external_liquidity = liquidity_analysis.get('external_liquidity', {})
            # Tutarlı isimlendirme ile timeframe_levels verilerini al
            timeframe_levels = all_symbol_data.get('timeframe_levels', {})
            candles = all_symbol_data.get('candles')
            
            # 🏆 YÜKSEK ÖNCELİK: Monday Range Reversal Strategy
            monday_signal = self._check_monday_range_reversal(symbol, timeframe_levels, candles)
            if monday_signal:
                monday_signal['priority_level'] = 6
                potential_signals.append(monday_signal)
                direction = monday_signal.get('direction', 'UNKNOWN')
                direction_str = direction.upper() if direction else 'UNKNOWN'
                logger.info(f"[{symbol}] 🏆 Monday Range Reversal Sinyali eklendi: {direction_str}")

            # 🥇 Öncelik 1: LIQSFP_REV (Stop Hunt/Reversal) - Artık liquidity_analyzer'dan hazır sinyaller alınıyor
            liquidity_signals = liquidity_analysis.get('signals', [])
            liqsfp_signals = [s for s in liquidity_signals if isinstance(s, dict) and s.get('type') == 'LIQSFP_REV']

            if liqsfp_signals:
                for liqsfp_signal in liqsfp_signals:
                    if isinstance(liqsfp_signal, dict):
                        # Symbol bilgisini ekle (eğer yoksa)
                        if not liqsfp_signal.get('symbol'):
                            liqsfp_signal['symbol'] = symbol
                        potential_signals.append(liqsfp_signal)
                        direction = liqsfp_signal.get('direction', 'UNKNOWN')
                        price = liqsfp_signal.get('price', 'N/A')
                        logger.info(f"[{symbol}] 🥇 LIQSFP_REV Sinyali alındı: {direction} @ {price}")
                    else:
                        logger.warning(f"[{symbol}] ⚠️ LIQSFP sinyali geçersiz format: {type(liqsfp_signal)}")
            else:
                logger.debug(f"[{symbol}] 🥇 LIQSFP_REV: liquidity_analyzer'dan hazır sinyal bulunamadı.")

            #  Öncelik 2: BRKR12_BOS1 (Breaker+BOS)
            # Bu kısım büyük ölçüde doğru görünüyor, breaker ve bos verilerini kontrol ediyor.



            # 🏅 Öncelik 4a: OTE + ORDER BLOCK CONFLUENCE (Fibonacci OTE + OB kesişimi)
            ote_ob_signal = self._check_ote_ob_confluence_signal(symbol, all_symbol_data)
            if ote_ob_signal:
                ote_ob_signal['priority_level'] = 9
                potential_signals.append(ote_ob_signal)
                direction = ote_ob_signal.get('direction', 'UNKNOWN')
                direction_str = direction.upper() if direction else 'UNKNOWN'
                logger.info(f"[{symbol}] 🏅 OTE + ORDER BLOCK CONFLUENCE Sinyali eklendi: {direction_str}")
            
            # 🏅 Öncelik 4b: ICT FVG_OB_CONFLUENCE (Gerçek ICT Konsepti)
            fvg_enhanced_obs = all_symbol_data.get('fvg_enhanced_obs', [])
            if fvg_enhanced_obs:
                for fvg_ob in fvg_enhanced_obs:
                    confluence_score = fvg_ob.get('confluence_score', 0)
                    
                    # ICT confluence'dan optimal giriş seviyesini al
                    entry_price = fvg_ob.get('entry_price')
                    is_ote_enhanced = fvg_ob.get('is_ote_enhanced', False)
                    
                    if entry_price:
                        # Confidence hesaplama - ICT kurallarına göre
                        base_confidence = min(0.95, 0.6 + (confluence_score / 200))  # Skor bazlı confidence
                        if is_ote_enhanced:
                            base_confidence = min(0.95, base_confidence + 0.1)  # OTE bonus
                        
                        confluence_type = "OTE Enhanced" if is_ote_enhanced else "Standard"
                        
                        fvg_ob_signal = {
                            'type': 'FVG_OB_CONFLUENCE',
                            'symbol': symbol,
                            'direction': fvg_ob.get('direction'),
                            'price': entry_price,
                            'confidence': base_confidence,
                            'reason': f'ICT {confluence_type} FVG-OB Confluence',
                            'confluence_score': confluence_score,
                            'is_ote_enhanced': is_ote_enhanced,
                            'spatial_score': fvg_ob.get('spatial_score', 0),
                            'temporal_score': fvg_ob.get('temporal_score', 0),
                            'ote_score': fvg_ob.get('ote_score', 0),
                            'swing_range': fvg_ob.get('swing_range', {}),
                            'details': fvg_ob,
                            'priority_level': 10
                        }
                        potential_signals.append(fvg_ob_signal)
                        direction = fvg_ob.get('direction', 'UNKNOWN')
                        direction_str = direction.upper() if direction else 'UNKNOWN'
                        logger.info(f"[{symbol}] 🏅 ICT FVG-OB Confluence Sinyali eklendi: {direction_str} (Skor: {confluence_score:.1f})")

            # 🏅+ YENİ GELİŞMİŞ ICT KONSEPTLERİ
            # Öncelik 5: VOLUME_IMBALANCE (Yüksek kalite VI sinyalleri)
            vi_signal = self._check_volume_imbalance_signal(symbol, all_symbol_data)
            if vi_signal:
                vi_signal['priority_level'] = 11
                potential_signals.append(vi_signal)
                direction = vi_signal.get('direction', 'UNKNOWN')
                direction_str = direction.upper() if direction else 'UNKNOWN'
                logger.info(f"[{symbol}] 🏅 VOLUME_IMBALANCE Sinyali eklendi: {direction_str}")

            # Öncelik 6: REJECTION_BLOCK (Güçlü rejection block sinyalleri)
            rb_signal = self._check_rejection_block_signal(symbol, all_symbol_data)
            if rb_signal:
                rb_signal['priority_level'] = 12
                potential_signals.append(rb_signal)
                direction = rb_signal.get('direction', 'UNKNOWN')
                direction_str = direction.upper() if direction else 'UNKNOWN'
                logger.info(f"[{symbol}] 🏅 REJECTION_BLOCK Sinyali eklendi: {direction_str}")

            # KALDIRILAN: EQUAL_HIGHS_LOWS artık kendi başına sinyal üretmez
            # Bu konsept artık "Draw on Liquidity" mantığıyla confluence faktörü olarak kullanılır

            # Öncelik 8: TURTLE_SOUP_IFVG (False Breakout + Inverse FVG Confluence)
            turtle_soup_signal = self._check_turtle_soup_ifvg_signal(symbol, all_symbol_data)
            if turtle_soup_signal:
                turtle_soup_signal['priority_level'] = 14
                potential_signals.append(turtle_soup_signal)
                direction = turtle_soup_signal.get('direction', 'UNKNOWN')
                direction_str = direction.upper() if direction else 'UNKNOWN'
                logger.info(f"[{symbol}] 🏅 TURTLE_SOUP_IFVG Sinyali eklendi: {direction_str}")

            # Öncelik 9: BREAKER_RETEST (Breaker Block Retest Sinyalleri)
            breaker_retest_signal = self._check_breaker_retest_signal(symbol, all_symbol_data)
            if breaker_retest_signal:
                breaker_retest_signal['priority_level'] = 15
                potential_signals.append(breaker_retest_signal)
                direction = breaker_retest_signal.get('direction', 'UNKNOWN')
                direction_str = direction.upper() if direction else 'UNKNOWN'
                logger.info(f"[{symbol}] 🏅 BREAKER_RETEST Sinyali eklendi: {direction_str}")

            # Tüm potansiyel sinyalleri döndür
            if potential_signals:
                logger.success(f"[{symbol}] Toplam {len(potential_signals)} potansiyel sinyal bulundu")
                return potential_signals
            else:
                logger.info(f"[{symbol}] Hiçbir ICT potansiyel sinyal bulunamadı - tüm öncelikler kontrol edildi")
                return []
                
        except Exception as e:
            import traceback
            logger.error(f"[{symbol}] Ana sinyal belirleme hatası: {e}")
            logger.error(f"[{symbol}] Hata detayı: {traceback.format_exc()}")
            return None

    def _check_ifvg_retest_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        iFVG (Inversion Fair Value Gap) bölgelerinin yeniden test edilmesini kontrol eder.
        Bu, çok yüksek olasılıklı bir ICT ticaret kurulumudur.

        Args:
            symbol (str): Ticaret sembolü.
            all_symbol_data (Dict[str, Any]): Sembole ait tüm analiz verileri.

        Returns:
            Optional[Dict[str, Any]]: Geçerli bir iFVG retest sinyali veya None.
        """
        try:
            # iFVG analiz verisini al - ifvg_analyzer her zaman tutarlı sözlük döndürür
            ifvg_analysis_data = all_symbol_data.get('ifvg_analysis', {})
            ifvg_list = ifvg_analysis_data.get('inverse_fvgs', [])
            candles = all_symbol_data.get('candles')

            if not ifvg_list or candles is None or candles.empty:
                return None

            current_price = candles.iloc[-1]['close']

            # En son oluşan iFVG'leri kontrol et (performans için)
            for ifvg in reversed(ifvg_list[-5:]): # Son 5 iFVG
                if ifvg.get('is_tested'):
                    continue

                ifvg_top = ifvg.get('top', 0)
                ifvg_bottom = ifvg.get('bottom', 0)
                ifvg_role = ifvg.get('role', '')

                # Fiyat iFVG bölgesine girdi mi?
                if ifvg_bottom <= current_price <= ifvg_top:
                    direction = ''
                    reason = ''

                    # Bullish iFVG (Destek) testi
                    if ifvg_role == 'bullish_support':
                        direction = 'bull'
                        reason = f"Bullish iFVG (eski direnç) destek olarak test ediliyor @ {ifvg_bottom:.4f}"

                    # Bearish iFVG (Direnç) testi
                    elif ifvg_role == 'bearish_resistance':
                        direction = 'bear'
                        reason = f"Bearish iFVG (eski destek) direnç olarak test ediliyor @ {ifvg_top:.4f}"

                    if direction:
                        return {
                            'type': 'IFVG_RETEST',
                            'direction': direction,
                            'price': current_price,
                            'confidence': 0.90, # iFVG retest çok yüksek güvenilirlikli bir sinyaldir
                            'confluence_score': 0.90 * 100,
                            'reason': reason,
                            'details': ifvg
                        }
            return None
        except Exception as e:
            logger.error(f"[{symbol}] iFVG retest sinyali kontrolü sırasında hata: {e}", exc_info=True)
            return None











    def _score_eq_levels(self, symbol: str, timeframe: str, timeframe_levels: Optional[Dict[str, Any]], stats: Optional[Dict[str, Any]], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        EQ seviyeleri puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            timeframe_levels: Timeframe seviyeleri
            stats: İstatistik verileri
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if not timeframe_levels or not stats or not trade_direction:
            return scores

        last_price = stats.get("last_price")
        if not last_price:
            return scores

        # EQ seviyeleri kontrolü
        eq_checks = {
            'eqd': ("🟨EQD", "Daily EQ"),
            'eqw': ("🟦EQW", "Weekly EQ"),
            'eqm': ("🟣EQM", "Monthly EQ"),
            'mreq': ("📅MREQ", "Monday Range EQ")
        }

        for eq_key, (emoji, label) in eq_checks.items():
            eq_level = timeframe_levels.get(eq_key)

            if eq_level:
                if eq_key == 'mreq':
                    logger.info(f"{symbol} - MREq control: Last price {self.format_price(last_price)} - MREq value {self.format_price(eq_level)}")

                # Pozitif puanlama
                if last_price > eq_level and trade_direction == "bull":
                    scores.append((0.5, f"{emoji}⬆️"))
                    if eq_key == 'mreq':
                        logger.info(f"{symbol} - MREq: +0.5 point added (Price above MREq and bullish direction)")
                elif last_price < eq_level and trade_direction == "bear":
                    scores.append((0.5, f"{emoji}⬇️"))
                    if eq_key == 'mreq':
                        logger.info(f"{symbol} - MREq: +0.5 point added (Price below MREq and bear direction)")

                # Negatif puanlama
                if last_price < eq_level and trade_direction == "bull":
                    scores.append((-0.5, f"🔴 Neg {eq_key.upper()}"))
                    logger.debug(f"[{symbol}] Negative Score: -0.5 (Bull PAT Below {eq_key.upper()})")
                elif last_price > eq_level and trade_direction == "bear":
                    scores.append((-0.5, f"🔴 Neg {eq_key.upper()}"))
                    logger.debug(f"[{symbol}] Negative Score: -0.5 (Bear PAT Above {eq_key.upper()})")

        return scores

    def _score_divergence(self, symbol: str, timeframe: str, divergences: Optional[List[Dict[str, Any]]], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        Divergence puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            divergences: Divergence listesi
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        # 720 ve D zaman dilimlerinde divergence puanlaması yapma
        if timeframe == "720" or timeframe == "D":
            logger.info(f"[{symbol}] {timeframe} zaman diliminde divergence puanlaması atlanıyor.")
            return scores

        if not divergences or not trade_direction:
            return scores

        tf_label = self._get_timeframe_label(timeframe)
        divergence_score = 0.5
        negative_divergence_score = -0.5

        # Her divergence için
        for div in divergences:
            div_type = div.get("type", "")
            # "Hidden" içerip içermediğini kontrol et
            hidden_prefix = "Hid " if "Hidden" in div_type else ""

            # Pozitif puanlama - yön uyumlu
            if ("Positive" in div_type or "Bullish" in div_type) and trade_direction == "bull":
                # Etiketi kısalt (örn: ✅ 4s Hid Bull Div)
                label = f"✅ {tf_label} {hidden_prefix}Bull Div"
                scores.append((divergence_score, label))
                logger.debug(f"[{symbol} {tf_label}] Direction-aligned Divergence score added: +{divergence_score} ({div_type})")
            elif ("Negative" in div_type or "Bearish" in div_type) and trade_direction == "bear":
                # Etiketi kısalt (örn: ✅ 4s Hid Bear Div)
                label = f"✅ {tf_label} {hidden_prefix}Bear Div"
                scores.append((divergence_score, label))
                logger.debug(f"[{symbol} {tf_label}] Direction-aligned Divergence score added: +{divergence_score} ({div_type})")

            # Negatif puanlama
            elif ("Positive" in div_type or "Bullish" in div_type) and trade_direction == "bear":
                # Etiketi kısalt ve "Hidden" bilgisini ekle
                label = f"🔴 Neg {tf_label} {hidden_prefix}Bull Div"
                scores.append((negative_divergence_score, label))
                logger.debug(f"[{symbol}] Negative Score: {negative_divergence_score:.1f} (Bear PAT Reversed {hidden_prefix}Bull Div)")
            elif ("Negative" in div_type or "Bearish" in div_type) and trade_direction == "bull":
                 # Etiketi kısalt ve "Hidden" bilgisini ekle
                label = f"🔴 Neg {tf_label} {hidden_prefix}Bear Div"
                scores.append((negative_divergence_score, label))
                logger.debug(f"[{symbol}] Negative Score: {negative_divergence_score:.1f} (Bull PAT Reversed {hidden_prefix}Bear Div)")

        return scores







    def _score_supertrend(self, symbol: str, timeframe: str, supertrend: Optional[Dict[str, Any]], price_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        SuperTrend puanlaması

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if not supertrend or not price_direction:
            return scores

        st_trend = supertrend.get("trend")
        if not st_trend:
            return scores

        # Trend uyumu kontrolü
        st_bull = st_trend == "up"
        base_bull = price_direction == "bull"
        st_emoji = "⬆️" if st_trend == "up" else "⬇️"

        # SuperTrend sinyalini al
        st_signal = supertrend.get("signal", "").lower()

        # Pozitif puanlama - trend uyumlu
        if (st_bull and base_bull) or (not st_bull and not base_bull):
            scores.append((0.5, f"🟢 STrend Align ST{st_emoji}"))
            logger.debug(f"[{symbol}] SuperTrend trend uyumu: +0.5 puan (ST: {st_trend}, Pattern: {price_direction})")

            # Mesafe faktörü - yakın trendin başlangıcı
            st_distance_pct = supertrend.get("distance_percent", 0)
            if st_distance_pct < 2.0:
                scores.append((0.5, f"📱 STrend Close ({st_distance_pct:.2f}%)"))
                logger.debug(f"[{symbol}] SuperTrend mesafe puanı: +0.5 (Mesafe: {st_distance_pct:.2f}%)")

            # Yeni sinyal puanı - SADECE GERÇEK SİNYALLER İÇİN PUAN EKLE
            # "neutral", "nötr", "notr" gibi farklı yazımları kontrol et
            if st_signal and st_signal not in ["neutral", "nötr", "notr", "ntr", "n/a", "none"]:
                scores.append((0.5, f"STrend Signal ({st_signal})"))
                logger.debug(f"[{symbol}] SuperTrend sinyal puanı: +0.5 (Sinyal: {st_signal})")
            else:
                logger.debug(f"[{symbol}] SuperTrend sinyali nötr veya yok, puan eklenmedi. Sinyal: {st_signal}")

        # Negatif puanlama - trend uyumsuzluğu
        if (st_bull and not base_bull) or (not st_bull and base_bull):
            scores.append((-0.5, f"🔴 Neg ST: ST{st_emoji}"))
            logger.debug(f"[{symbol}] SuperTrend trend uyumsuzluğu: -0.5 puan (ST: {st_trend}, Pattern: {price_direction})")

        return scores







    

    def generate_score_report(self, all_timeframe_data: Dict[str, Dict[str, Dict]], min_score_threshold: float = 4.0) -> Dict[str, str]:
        """
        Potansiyel sinyaller için, sadece kendisine verilen veriyi kullanarak bir rapor metni oluşturur.
        (self.scores gibi dış durumlara bağımlı değildir.)
        """
        symbol_reports = {}
        for symbol, timeframes_data in all_timeframe_data.items():
            report_lines = []
            for timeframe, data in timeframes_data.items():
                if timeframe != self.main_timeframe:
                    continue

                # Veri kaynağı: Argüman olarak gelen 'data' (score_result içerir)
                score_result = data.get("score_result")
                if not score_result:
                    continue

                net_score = score_result.get('net_score', 0.0)
                if net_score < min_score_threshold:
                    continue

                # DÜZELTME: trade_direction veya direction kullan, öncelik trade_direction
                direction = score_result.get('trade_direction')
                if direction is None:
                    direction = score_result.get('direction', 'N/A')
                direction_emoji = "🔼" if direction == 'bull' else "🔻"

                # Veriyi her zaman score_result'tan al, self.scores'tan değil.
                entry = score_result.get('entry_price')
                sl = score_result.get('sl_price')
                tp1 = score_result.get('tp1_price')
                tp2 = score_result.get('tp2_price')
                tp3 = score_result.get('tp3_price')
                
                if entry is None or sl is None:
                    logger.debug(f"[{symbol}] Rapor oluşturma atlandı: Giriş veya SL fiyatı eksik.")
                    continue

                # SL ve TP yüzdelerini doğru hesapla
                if entry and sl:
                    if direction == 'bull':
                        sl_pct = ((entry - sl) / entry) * 100  # Bullish: entry'den düşük SL
                    else:
                        sl_pct = ((sl - entry) / entry) * 100  # Bearish: entry'den yüksek SL
                else:
                    sl_pct = 0.0

                # Timeframe formatı düzelt
                tf_display = "4h" if timeframe == "240" else timeframe

                # Yön metni düzelt
                direction_text = "LONG" if direction == 'bull' else "SHORT" if direction == 'bear' else "UNKNOWN"
                report_lines.append(f"🎯 {symbol} | {tf_display} | 📊 Net Score: {net_score:.1f} | {direction_emoji} {direction_text} |")
                report_lines.append(f" ---------------------------------")

                base_score = score_result.get('base_score', 0)
                conf_score = score_result.get('confirmation_score', 0)
                neg_score = score_result.get('negative_score', 0)
                report_lines.append(f"📊 Base: {base_score:.1f}, ")
                report_lines.append(f"      Conf. {conf_score:.1f}, ")
                report_lines.append(f"      Neg. {neg_score:.1f}")
                report_lines.append("")

                # R:R hesaplaması düzelt
                if tp1 and entry and sl:
                    if direction == 'bull':
                        risk = entry - sl
                        reward = tp1 - entry
                    else:
                        risk = sl - entry  
                        reward = entry - tp1
                    
                    risk_reward_ratio = reward / risk if risk > 0 else 0
                else:
                    risk_reward_ratio = 0

                report_lines.append(f"💰 Trade Levels: R:R: {risk_reward_ratio:.2f}")
                report_lines.append(f"----------------------------")
                
                # Volatilite seviyesi ve strateji bilgilerini ekle
                volatility_level = score_result.get("volatility_level", "unknown")
                if volatility_level != "unknown":
                    report_lines.append(f"      🌊 Volatilite: {volatility_level.upper()}")
                
                # Fibonacci seviyesi bilgisini ekle
                fib_level = score_result.get("fib_level")
                if fib_level:
                    report_lines.append(f"      📏 Fib Seviyesi: {fib_level}")
                
                # Kullanılan strateji bilgisini ekle
                strategy_used = score_result.get("strategy_used", "")
                if strategy_used:
                    if "fvg_fibonacci" in strategy_used.lower():
                        report_lines.append(f"      📈 Strateji: FVG+Fib")
                    elif "fibonacci" in strategy_used.lower():
                        if strategy_used == "fibonacci_distant":
                            report_lines.append(f"      📈 Strateji: Fib (⚠️ Uzak)")
                        else:
                            report_lines.append(f"      📈 Strateji: Fib")
                
                report_lines.append(f"      Entry: {self.format_price(entry)} ")
                report_lines.append(f"      SL: {self.format_price(sl)} ({sl_pct:.2f}%) ")
                
                # TP yüzdelerini doğru hesapla
                if tp1 and entry:
                    if direction == 'bull':
                        tp1_pct = ((tp1 - entry) / entry) * 100
                    else:
                        tp1_pct = ((entry - tp1) / entry) * 100
                    report_lines.append(f"      TP1: {self.format_price(tp1)} ({tp1_pct:.2f}%) ")
                
                if tp2 and entry:
                    if direction == 'bull':
                        tp2_pct = ((tp2 - entry) / entry) * 100
                    else:
                        tp2_pct = ((entry - tp2) / entry) * 100
                    report_lines.append(f"      TP2: {self.format_price(tp2)} ({tp2_pct:.2f}%) ")
                
                if tp3 and entry:
                    if direction == 'bull':
                        tp3_pct = ((tp3 - entry) / entry) * 100
                    else:
                        tp3_pct = ((entry - tp3) / entry) * 100
                    report_lines.append(f"      TP3: {self.format_price(tp3)} ({tp3_pct:.2f}%) ")
                
                report_lines.append("")
                
                # Base Signals detaylarını ekle
                base_details = score_result.get('base_details', [])
                if base_details:
                    report_lines.append(f"🔍 Base Signals: ")
                    report_lines.append(f"----------------------------")
                    report_lines.append("     - " + ", ".join([detail[1] if isinstance(detail, (list, tuple)) and len(detail) > 1 else str(detail) for detail in base_details]) + " -")
                    report_lines.append("")

                # Premium/Discount bilgisini ekle
                pd_label = score_result.get("pd_label", "N/A")
                eq_level = score_result.get('eq_level', 'N/A')
                if eq_level != 'N/A' and isinstance(eq_level, (int, float)):
                    eq_level_formatted = self.format_price(eq_level)
                    report_lines.append(f"📍 {pd_label} (EQ: {eq_level_formatted}) ")
                else:
                    report_lines.append(f"📍 {pd_label} ")

                # Order Blocks bilgisini ekle
                bull_ob = "N/A"
                bear_ob = "N/A"
                if "bull_ob" in score_result:
                    bull_ob = "Yes" if score_result["bull_ob"] else "No"
                if "bear_ob" in score_result:
                    bear_ob = "Yes" if score_result["bear_ob"] else "No"

                report_lines.append("----------------------------")
                report_lines.append(f"    OBs: Bull: {bull_ob}, ")
                report_lines.append(f"    Bear: {bear_ob}")
                report_lines.append(" ---------------------------")

                # ---> YENİ BLOK: Trend Rejimi Bilgisini Ekle <---
                regime = score_result.get('regime', 'Belirsiz').capitalize()
                if regime != 'Belirsiz':
                    report_lines.append(f"📊 Trend: {regime}")
                    report_lines.append(" ---------------------------")
                # --- YENİ BLOK SONU ---

                # Confirmations ekle
                confirmation_details = score_result.get('confirmation_details', [])
                if confirmation_details:
                    report_lines.append("")
                    report_lines.append("✅ Confirmations:")
                    for detail in confirmation_details:
                        if isinstance(detail, (list, tuple)) and len(detail) > 1:
                            report_lines.append(f"  {detail[1]}")
                        else:
                            report_lines.append(f"  {str(detail)}")

                # Negatives/Conflicts ekle
                negative_details = score_result.get('negative_details', [])
                if negative_details:
                    report_lines.append("")
                    report_lines.append("⛔ Conflicts:")
                    for detail in negative_details:
                        if isinstance(detail, (list, tuple)) and len(detail) > 1:
                            report_lines.append(f"  {detail[1]}")
                        else:
                            report_lines.append(f"  {str(detail)}")

            if report_lines:
                symbol_reports[symbol] = "\n".join(report_lines)

        return symbol_reports




    











    


    def _check_monday_range_reversal(self, symbol: str, timeframe_levels: Dict[str, Any], 
                                   candles: Optional[pd.DataFrame]) -> Optional[Dict[str, Any]]:
        """
        Monday Range Reversal Strategy Implementation
        
        Analiz adımları:
        1. Monday Range (MR) seviyelerini al
        2. Fiyatın MR'nin dışına sapmasını (deviation) tespit et
        3. Ardından MR'ye geri kapanışını (reclaim) kontrol et
        4. Bu durum tespit edilirse MONDAY_RANGE_REVERSAL sinyali üret
        
        Args:
            symbol: Symbol adı
            timeframe_levels: Timeframe levels analizinden gelen veriler
            candles: Mum verileri (1h/4h kapanış kontrolü için)
            
        Returns:
            Dict: Monday Range Reversal sinyali veya None
        """
        try:
            # ✅ DÜZELTME: DataFrame ambiguous hatası için doğru kontrol
            if not timeframe_levels:
                return None
            if candles is None or candles.empty:
                return None

            # ✅ EK GÜVENLIK: DataFrame tipini de kontrol et
            if not isinstance(candles, pd.DataFrame):
                logger.warning(f"[{symbol}] Monday Range Reversal: candles DataFrame değil, tip: {type(candles)}")
                return None
                
            # Monday Range seviyelerini al - Tutarlı isimlendirme
            monday_range = timeframe_levels.get('mr')  # 'mr' anahtarı kullanılıyor
            if not monday_range or monday_range == [None, None]:
                logger.debug(f"[{symbol}] Monday Range verisi bulunamadı")
                return None
                
            monday_low, monday_high = monday_range
            monday_range_eq = timeframe_levels.get('mreq')  # 'mreq' anahtarı kullanılıyor
            
            if not monday_low or not monday_high:
                return None
                
            logger.debug(f"[{symbol}] Monday Range: {monday_low:.6f} - {monday_high:.6f}")
            
            # Son 20 mum için analiz (1h + 4h mumları)
            recent_candles = candles.tail(20)
            current_price = float(candles.iloc[-1]['close'])
            
            # Deviation + Reclaim pattern arama
            deviation_found = False
            reclaim_found = False
            deviation_direction = None
            deviation_candle_idx = None
            
            # 1. Deviation kontrolü: Fiyatın MR dışına çıkması
            for i, (idx, candle) in enumerate(recent_candles.iterrows()):
                candle_high = float(candle['high'])
                candle_low = float(candle['low'])
                candle_close = float(candle['close'])
                
                # Yukarı sapma (Above Monday High)
                if candle_high > monday_high and not deviation_found:
                    logger.info(f"[{symbol}] 📈 Monday Range YUKARI sapması tespit edildi: {candle_high:.6f} > {monday_high:.6f}")
                    deviation_found = True
                    deviation_direction = 'above'
                    deviation_candle_idx = i
                
                # Aşağı sapma (Below Monday Low)
                elif candle_low < monday_low and not deviation_found:
                    logger.info(f"[{symbol}] 📉 Monday Range AŞAĞI sapması tespit edildi: {candle_low:.6f} < {monday_low:.6f}")
                    deviation_found = True
                    deviation_direction = 'below'
                    deviation_candle_idx = i
                    
            # 2. Reclaim kontrolü: Sapma sonrası MR'ye geri kapanış
            if deviation_found and deviation_candle_idx is not None:
                # Deviation sonrasındaki mumları kontrol et
                post_deviation_candles = recent_candles.iloc[deviation_candle_idx + 1:]
                
                for i, (idx, candle) in enumerate(post_deviation_candles.iterrows()):
                    candle_close = float(candle['close'])
                    
                    if deviation_direction == 'above':
                        # Yukarı sapma sonrası, MR içine kapanış
                        if candle_close < monday_high and candle_close > monday_low:
                            logger.success(f"[{symbol}] ✅ Monday Range RECLAIM: Yukarı sapma sonrası MR içine kapanış @ {candle_close:.6f}")
                            reclaim_found = True
                            break
                            
                    elif deviation_direction == 'below':
                        # Aşağı sapma sonrası, MR içine kapanış
                        if candle_close > monday_low and candle_close < monday_high:
                            logger.success(f"[{symbol}] ✅ Monday Range RECLAIM: Aşağı sapma sonrası MR içine kapanış @ {candle_close:.6f}")
                            reclaim_found = True
                            break
            
            # 3. Monday Range Reversal sinyali üret
            if deviation_found and reclaim_found:
                # ✅ EK KONTROL: Mevcut fiyat hala range içinde mi?
                current_in_range = monday_low <= current_price <= monday_high

                if not current_in_range:
                    logger.warning(f"[{symbol}] ⚠️ Monday Range Reversal: Reclaim tespit edildi ama mevcut fiyat ({current_price:.6f}) range dışında ({monday_low:.6f}-{monday_high:.6f})")
                    return None

                # Reversal yönünü belirle
                if deviation_direction == 'above':
                    # Yukarı sapma + reclaim = Bearish reversal
                    signal_direction = 'bear'
                    target_price = monday_range_eq if monday_range_eq else monday_low
                    reasoning = f"Monday Range yukarı sapma ({monday_high:.6f}) sonrası içe kapanış - Bearish reversal"

                elif deviation_direction == 'below':
                    # Aşağı sapma + reclaim = Bullish reversal
                    signal_direction = 'bull'
                    target_price = monday_range_eq if monday_range_eq else monday_high
                    reasoning = f"Monday Range aşağı sapma ({monday_low:.6f}) sonrası içe kapanış - Bullish reversal"
                else:
                    return None
                
                # Güven seviyesi hesapla
                confidence = self._calculate_monday_range_confidence(
                    deviation_direction, current_price, monday_low, monday_high, monday_range_eq
                )
                
                logger.success(f"[{symbol}] 🏆 MONDAY_RANGE_REVERSAL sinyali! Yön: {signal_direction.upper()}, Güven: {confidence:.2f}")
                
                return {
                    'type': 'MONDAY_RANGE_REVERSAL',
                    'symbol': symbol,
                    'direction': signal_direction,
                    'price': target_price,
                    'confidence': confidence,
                    'confluence_score': confidence * 100,
                    'reason': reasoning,
                    'pattern_details': {
                        'monday_range': [monday_low, monday_high],
                        'monday_range_eq': monday_range_eq,
                        'deviation_direction': deviation_direction,
                        'current_price': current_price,
                        'price_in_range_at_signal': current_in_range  # ✅ Sinyal üretildiği anda fiyatın range içindeki konumu
                    }
                }
            
            # Pattern henüz tamamlanmamış
            if deviation_found and not reclaim_found:
                logger.info(f"[{symbol}] ⏳ Monday Range deviation mevcut ({deviation_direction}), reclaim bekleniyor...")
                
            return None
            
        except Exception as e:
            logger.error(f"[{symbol}] Monday Range Reversal analizi hatası: {e}")
            return None

    def _calculate_monday_range_confidence(self, deviation_direction: str, current_price: float,
                                         monday_low: float, monday_high: float, 
                                         monday_range_eq: Optional[float]) -> float:
        """Monday Range Reversal sinyali için güven seviyesi hesaplar"""
        
        base_confidence = 0.85  # Monday Range yüksek güvenilirlik stratejisi
        
        # Range boyutuna göre adjustment
        range_size = monday_high - monday_low
        price_range_pct = range_size / current_price
        
        # Küçük range'ler daha güvenilir (%0.5'ten küçük ideal)
        if price_range_pct < 0.005:  # %0.5'ten küçük
            base_confidence += 0.05
        elif price_range_pct > 0.02:  # %2'den büyük
            base_confidence -= 0.05
            
        # MR Equilibrium yakınlığı
        if monday_range_eq:
            distance_from_eq = abs(current_price - monday_range_eq) / monday_range_eq
            if distance_from_eq < 0.002:  # EQ'ya çok yakın
                base_confidence += 0.05
                
        # Deviation yönü - aşağı sapma genelde daha güçlü
        if deviation_direction == 'below':
            base_confidence += 0.03  # Sell-side liquidity hunt daha güçlü
            
        return min(0.95, base_confidence)

    # =================================================================================
    # ZAMAN UYUMLU GİRİŞ (HTF CONFLUENCE) ANALİZİ - ICT Gelişmiş Konsept
    # =================================================================================

    def analyze_htf_confluence(self, symbol: str, main_signal: Dict[str, Any],
                              all_timeframe_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Zaman Uyumlu Giriş (HTF Confluence) analizi yapar.
        
        Bu ICT konsepti, farklı zaman dilimlerindeki analizlerin aynı yönde
        olmasını kontrol eder. Confluence, trade kalitesini önemli ölçüde artırır.
        
        Args:
            symbol: İşlem sembolü
            main_signal: Ana sinyal (current timeframe)
            all_timeframe_data: Tüm zaman dilimlerindeki analiz verileri
            
        Returns:
            HTF confluence analiz sonuçları
        """
        if not all_timeframe_data:
            logger.warning(f"{symbol} için HTF confluence analizi: Zaman dilimi verisi yok")
            return self._empty_htf_confluence_result()
        
        direction = main_signal.get('direction', '')
        main_direction = direction.upper() if direction else 'UNKNOWN'
        main_tf = self.main_timeframe
        
        logger.info(f"🔄 {symbol} HTF Confluence analizi: Ana yön={main_direction}, TF={main_tf}")
        
        # 1. Zaman dilimi hierarchy tanımla
        tf_hierarchy = self._define_timeframe_hierarchy(main_tf)
        
        # 2. Her zaman diliminde confluence analizi
        tf_confluence_results = {}
        for tf in tf_hierarchy:
            if tf in all_timeframe_data:
                tf_data = all_timeframe_data[tf]
                confluence_result = self._analyze_timeframe_confluence(
                    tf, tf_data, main_direction
                )
                tf_confluence_results[tf] = confluence_result
        
        # 3. HTF Structure bias analizi
        structure_confluence = self._analyze_structure_confluence(
            tf_confluence_results, main_direction
        )
        
        # 4. Key level confluence analizi
        key_level_confluence = self._analyze_key_level_confluence(
            all_timeframe_data, main_signal
        )
        
        # 5. Overall confluence score hesapla
        overall_score = self._calculate_overall_confluence_score(
            tf_confluence_results, structure_confluence, key_level_confluence
        )
        
        # 6. Confluence grade belirle
        confluence_grade = self._determine_confluence_grade(overall_score)
        
        result = {
            'symbol': symbol,
            'main_signal': main_signal,
            'main_direction': main_direction,
            'main_timeframe': main_tf,
            'timeframe_hierarchy': tf_hierarchy,
            'tf_confluence_results': tf_confluence_results,
            'structure_confluence': structure_confluence,
            'key_level_confluence': key_level_confluence,
            'overall_score': overall_score,
            'confluence_grade': confluence_grade,
            'confluence_strength': self._get_confluence_strength(overall_score),
            'recommended_action': self._get_confluence_recommendation(confluence_grade),
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        logger.success(f"✅ {symbol} HTF Confluence: Grade={confluence_grade}, "
                      f"Score={overall_score:.1f}/10")
        
        return result

    def _define_timeframe_hierarchy(self, main_tf: str) -> List[str]:
        """
        Ana zaman dilimine göre hierarchy tanımlar.
        """
        # Tüm timeframe'leri dakika cinsine çevir
        tf_minutes = {
            '5': 5, '15': 15, '30': 30, '60': 60,
            '240': 240, '720': 720, 'D': 1440, 'W': 10080
        }
        
        main_minutes = tf_minutes.get(main_tf, 240)
        
        # Ana TF'den büyük olan TF'leri al (HTF)
        higher_tfs = []
        for tf, minutes in tf_minutes.items():
            if minutes > main_minutes:
                higher_tfs.append(tf)
        
        # Büyükten küçüğe sırala
        higher_tfs.sort(key=lambda x: tf_minutes[x], reverse=True)
        
        # Ana TF'yi de ekle
        hierarchy = higher_tfs + [main_tf]
        
        return hierarchy

    def _analyze_timeframe_confluence(self, timeframe: str, tf_data: Dict[str, Any],
                                    main_direction: str) -> Dict[str, Any]:
        """
        Belirli bir zaman diliminde confluence analizi yapar.
        """
        confluence_factors = []
        confluence_score = 0
        
        # 1. Market Structure analizi
        structure_data = tf_data.get('main_tf_structure', {})
        if structure_data:
            market_bias = structure_data.get('market_bias', 'NEUTRAL')
            if market_bias.upper() == main_direction:
                confluence_score += 2
                confluence_factors.append(f"Market Structure: {market_bias}")

        # 2. Order Block analizi
        ob_data = tf_data.get('main_tf_order_blocks', {})
        if ob_data:
            if main_direction == 'BULLISH':
                bullish_obs = ob_data.get('bullish_obs', [])
                if bullish_obs:
                    confluence_score += 1.5
                    confluence_factors.append(f"Bullish OB x{len(bullish_obs)}")
            else:  # BEARISH
                bearish_obs = ob_data.get('bearish_obs', [])
                if bearish_obs:
                    confluence_score += 1.5
                    confluence_factors.append(f"Bearish OB x{len(bearish_obs)}")
        
        # 3. FVG analizi
        fvg_data = tf_data.get('fvg_analysis', {})
        if fvg_data:
            fvgs = fvg_data.get('fvgs', [])
            matching_fvgs = [fvg for fvg in fvgs if (fvg.get('direction', '') or '').upper() == main_direction]
            if matching_fvgs:
                confluence_score += 1
                confluence_factors.append(f"{main_direction} FVG x{len(matching_fvgs)}")
        
        # 4. Supertrend analizi
        supertrend_data = tf_data.get('supertrend_analysis', {})
        if supertrend_data:
            trend_value = supertrend_data.get('trend', '')
            trend = trend_value.upper() if trend_value else ''
            expected_trend = 'UP' if main_direction == 'BULLISH' else 'DOWN'
            if trend == expected_trend:
                confluence_score += 1
                confluence_factors.append(f"Supertrend: {trend}")

        # 5. Liquidity analizi
        liquidity_data = tf_data.get('liquidity_analysis', {})
        if liquidity_data:
            liqsfp_data = liquidity_data.get('liqsfp_analysis', {})
            detected_liqsfp = liqsfp_data.get('detected_liqsfp', [])
            matching_liqsfp = [l for l in detected_liqsfp if (l.get('direction', '') or '').upper() == main_direction]
            if matching_liqsfp:
                confluence_score += 2  # LIQSFP yüksek önem
                confluence_factors.append(f"LIQSFP: {len(matching_liqsfp)} events")
        
        return {
            'timeframe': timeframe,
            'confluence_score': confluence_score,
            'confluence_factors': confluence_factors,
            'max_possible_score': 7.5,  # 2+1.5+1+1+2
            'confluence_pct': (confluence_score / 7.5) * 100
        }

    def _analyze_structure_confluence(self, tf_confluence_results: Dict[str, Dict[str, Any]],
                                    main_direction: str) -> Dict[str, Any]:
        """
        Yapısal confluence analizi (tüm TF'lerde structure alignment).
        """
        structure_alignment = []
        total_weight = 0
        aligned_weight = 0
        
        # Timeframe ağırlıkları (daha yüksek TF'ler daha önemli)
        tf_weights = {
            'W': 5, 'D': 4, '720': 3, '240': 2, '60': 1, '30': 0.5, '15': 0.3, '5': 0.1
        }
        
        for tf, confluence_data in tf_confluence_results.items():
            weight = tf_weights.get(tf, 1)
            total_weight += weight
            
            confluence_pct = confluence_data.get('confluence_pct', 0)
            if confluence_pct >= 60:  # %60'dan fazla confluence
                aligned_weight += weight
                structure_alignment.append({
                    'timeframe': tf,
                    'confluence_pct': confluence_pct,
                    'weight': weight,
                    'status': 'ALIGNED'
                })
            else:
                structure_alignment.append({
                    'timeframe': tf,
                    'confluence_pct': confluence_pct,
                    'weight': weight,
                    'status': 'NOT_ALIGNED'
                })
        
        # Structure alignment score
        alignment_score = (aligned_weight / total_weight) * 100 if total_weight > 0 else 0
        
        return {
            'structure_alignment': structure_alignment,
            'alignment_score': alignment_score,
            'aligned_timeframes': len([a for a in structure_alignment if a['status'] == 'ALIGNED']),
            'total_timeframes': len(structure_alignment)
        }

    def _analyze_key_level_confluence(self, all_timeframe_data: Dict[str, Dict[str, Any]],
                                    main_signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Key level confluence analizi (aynı fiyat seviyelerinde çakışma).
        """
        signal_price = main_signal.get('price', 0)
        if signal_price == 0:
            return {'key_level_matches': [], 'confluence_count': 0}
        
        key_level_matches = []
        tolerance_pct = 0.002  # %0.2 tolerance
        
        for tf, tf_data in all_timeframe_data.items():
            # Order Block key levels
            ob_data = tf_data.get('main_tf_order_blocks', {})
            if ob_data:
                all_obs = ob_data.get('bullish_obs', []) + ob_data.get('bearish_obs', [])
                for ob in all_obs:
                    ob_mid = (float(ob.get('high', 0)) + float(ob.get('low', 0))) / 2
                    if self._is_price_near(signal_price, ob_mid, tolerance_pct):
                        key_level_matches.append({
                            'timeframe': tf,
                            'type': 'ORDER_BLOCK',
                            'level': ob_mid,
                            'ob_type': ob.get('type', ''),
                            'distance_pct': abs(signal_price - ob_mid) / signal_price * 100
                        })
            
            # FVG key levels
            fvg_data = tf_data.get('fvg_analysis', {})
            if fvg_data:
                fvgs = fvg_data.get('fvgs', [])
                for fvg in fvgs:
                    fvg_mid = (float(fvg.get('high', 0)) + float(fvg.get('low', 0))) / 2
                    if self._is_price_near(signal_price, fvg_mid, tolerance_pct):
                        key_level_matches.append({
                            'timeframe': tf,
                            'type': 'FVG',
                            'level': fvg_mid,
                            'fvg_direction': fvg.get('direction', ''),
                            'distance_pct': abs(signal_price - fvg_mid) / signal_price * 100
                        })
            
            # Fibonacci key levels
            fib_data = tf_data.get('fibonacci_analysis', {})
            if fib_data:
                fib_levels = fib_data.get('fibonacci_levels', [])
                for fib_level in fib_levels:
                    fib_price = float(fib_level.get('price', 0))
                    if self._is_price_near(signal_price, fib_price, tolerance_pct):
                        key_level_matches.append({
                            'timeframe': tf,
                            'type': 'FIBONACCI',
                            'level': fib_price,
                            'fib_ratio': fib_level.get('ratio', ''),
                            'distance_pct': abs(signal_price - fib_price) / signal_price * 100
                        })
        
        return {
            'key_level_matches': key_level_matches,
            'confluence_count': len(key_level_matches),
            'unique_timeframes': len(set(match['timeframe'] for match in key_level_matches))
        }

    def _calculate_overall_confluence_score(self, tf_confluence_results: Dict[str, Dict[str, Any]],
                                          structure_confluence: Dict[str, Any],
                                          key_level_confluence: Dict[str, Any]) -> float:
        """
        Genel confluence skorunu hesaplar (0-10 arası).
        """
        total_score = 0
        
        # 1. Timeframe confluence (40% ağırlık)
        tf_scores = [result.get('confluence_pct', 0) for result in tf_confluence_results.values()]
        avg_tf_score = sum(tf_scores) / len(tf_scores) if tf_scores else 0
        total_score += (avg_tf_score / 100) * 4  # 0-4 puan
        
        # 2. Structure alignment (35% ağırlık)
        structure_score = structure_confluence.get('alignment_score', 0)
        total_score += (structure_score / 100) * 3.5  # 0-3.5 puan
        
        # 3. Key level confluence (25% ağırlık)
        key_level_count = key_level_confluence.get('confluence_count', 0)
        unique_tf_count = key_level_confluence.get('unique_timeframes', 0)
        
        # Key level skoru: Unique TF sayısına göre
        if unique_tf_count >= 3:
            key_level_score = 2.5
        elif unique_tf_count == 2:
            key_level_score = 1.5
        elif unique_tf_count == 1:
            key_level_score = 0.8
        else:
            key_level_score = 0
        
        total_score += key_level_score  # 0-2.5 puan
        
        return min(10, max(0, total_score))

    def _determine_confluence_grade(self, overall_score: float) -> str:
        """
        Confluence skorunu grade'e çevirir.
        """
        if overall_score >= 8.5:
            return 'A+'
        elif overall_score >= 7.5:
            return 'A'
        elif overall_score >= 6.5:
            return 'B+'
        elif overall_score >= 5.5:
            return 'B'
        elif overall_score >= 4.5:
            return 'C+'
        elif overall_score >= 3.5:
            return 'C'
        elif overall_score >= 2.5:
            return 'D'
        else:
            return 'F'

    def _get_confluence_strength(self, overall_score: float) -> str:
        """
        Confluence gücünü tanımlar.
        """
        if overall_score >= 8.0:
            return 'EXCELLENT'
        elif overall_score >= 6.5:
            return 'STRONG'
        elif overall_score >= 5.0:
            return 'GOOD'
        elif overall_score >= 3.5:
            return 'FAIR'
        elif overall_score >= 2.0:
            return 'WEAK'
        else:
            return 'POOR'

    def _get_confluence_recommendation(self, confluence_grade: str) -> str:
        """
        Confluence grade'ine göre tavsiye verir.
        """
        grade_recommendations = {
            'A+': 'HIGHLY_RECOMMENDED - Mükemmel confluence, yüksek kaliteli trade',
            'A': 'RECOMMENDED - Güçlü confluence, kaliteli trade fırsatı',
            'B+': 'CONSIDER - İyi confluence, trade değerlendirilebilir',
            'B': 'CONSIDER - Orta confluence, dikkatli trade',
            'C+': 'CAUTION - Zayıf confluence, risk yönetimi kritik',
            'C': 'AVOID - Düşük confluence, trade önerilmez',
            'D': 'AVOID - Çok düşük confluence',
            'F': 'AVOID - Confluence yok, trade yapmayın'
        }
        
        return grade_recommendations.get(confluence_grade, 'UNKNOWN')

    def _is_price_near(self, price1: float, price2: float, tolerance_pct: float) -> bool:
        """
        İki fiyatın tolerance dahilinde yakın olup olmadığını kontrol eder.
        """
        if price1 == 0 or price2 == 0:
            return False
        
        distance_pct = abs(price1 - price2) / price1
        return distance_pct <= tolerance_pct

    def _empty_htf_confluence_result(self) -> Dict[str, Any]:
        """
        Boş HTF confluence analiz sonucu döner.
        """
        return {
            'symbol': '',
            'main_signal': {},
            'main_direction': '',
            'main_timeframe': '',
            'timeframe_hierarchy': [],
            'tf_confluence_results': {},
            'structure_confluence': {},
            'key_level_confluence': {},
            'overall_score': 0.0,
            'confluence_grade': 'F',
            'confluence_strength': 'POOR',
            'recommended_action': 'AVOID',
            'analysis_timestamp': datetime.now().isoformat()
        }

    def _check_volume_imbalance_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Volume Imbalance (Hacim Dengesizliği) sinyallerini kontrol eder.
        """
        try:
            vi_analysis = all_symbol_data.get('volume_imbalance', {})
            if not vi_analysis:
                return None
                
            trading_signals = vi_analysis.get('trading_signals', [])
            
            # Sadece EXCELLENT ve HIGH strength sinyalleri al
            high_quality_signals = [s for s in trading_signals 
                                  if s.get('signal_strength') in ['EXCELLENT', 'HIGH']]
            
            if high_quality_signals:
                # En iyi sinyali seç
                best_signal = max(high_quality_signals, 
                                key=lambda x: 1.0 if x.get('signal_strength') == 'EXCELLENT' else 0.8)
                
                direction = 'bull' if best_signal.get('direction') == 'BULLISH' else 'bear'
                confidence = 0.78 if best_signal.get('signal_strength') == 'EXCELLENT' else 0.72
                
                logger.success(f"[{symbol}] 🔥 Volume Imbalance sinyali: {direction.upper()}")
                return {
                    'type': 'VOLUME_IMBALANCE',
                    'direction': direction,
                    'price': best_signal.get('entry_zone', 0),
                    'confidence': confidence,
                    'confluence_score': confidence * 100,
                    'reason': best_signal.get('reasoning', 'High Quality Volume Imbalance'),
                    'vi_data': best_signal
                }
            
            return None
            
        except Exception as e:
            logger.error(f"[{symbol}] Volume Imbalance sinyal kontrolü hatası: {e}")
            return None

    def _check_rejection_block_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Rejection Block sinyallerini kontrol eder.
        """
        try:
            rb_analysis = all_symbol_data.get('rejection_block_analysis', {})
            if not rb_analysis:
                return None
                
            rb_signals = rb_analysis.get('signals', [])
            
            # Rejection Block sinyalleri genellikle güçlüdür, direkt al
            if rb_signals:
                # En son sinyali al (en güncel)
                latest_signal = rb_signals[-1]
                
                signal_type = latest_signal.get('type', '')
                direction = 'bull' if 'Bullish' in signal_type else 'bear'
                confidence = 0.75  # Rejection Block orta-yüksek güven
                
                logger.success(f"[{symbol}] 🔥 Rejection Block sinyali: {direction.upper()}")
                return {
                    'type': 'REJECTION_BLOCK',
                    'direction': direction,
                    'price': latest_signal.get('price', 0),
                    'confidence': confidence,
                    'confluence_score': confidence * 100,
                    'reason': latest_signal.get('reason', 'Rejection Block Signal'),
                    'rb_data': latest_signal
                }
            
            return None
            
        except Exception as e:
            logger.error(f"[{symbol}] Rejection Block sinyal kontrolü hatası: {e}")
            return None

    # KALDIRILAN: _check_equal_highs_lows_signal fonksiyonu
    # Bu fonksiyon artık "Draw on Liquidity" konseptine uygun olarak 
    # confluence faktörü olarak _analyze_eqhl_target_confluence ile değiştirildi

    def _calculate_advanced_ict_confluence(self, symbol: str, all_symbol_data: Dict[str, Any], 
                                         trade_direction: str, main_signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Gelişmiş ICT konseptleri için confluence puanlaması yapar.
        Ana sinyali güçlendiren veya zayıflatan faktörleri analiz eder.
        """
        try:
            confluence_result = {
                'positive_score': 0.0,
                'negative_score': 0.0,
                'positive_details': [],
                'negative_details': []
            }
            
            # 1. Weak/Strong Swings Confluence
            ws_analysis = all_symbol_data.get('weak_strong_swings', {})
            ws_confluence = self._analyze_weak_strong_confluence(ws_analysis, trade_direction)
            confluence_result['positive_score'] += ws_confluence['score']
            if ws_confluence['details']:
                confluence_result['positive_details'].append(ws_confluence['details'])
            
            # 2. Volume Imbalance Confluence  
            vi_analysis = all_symbol_data.get('volume_imbalance', {})
            vi_confluence = self._analyze_volume_imbalance_confluence(vi_analysis, trade_direction)
            confluence_result['positive_score'] += vi_confluence['score']
            if vi_confluence['details']:
                confluence_result['positive_details'].append(vi_confluence['details'])
            
            # 3. Equal Highs/Lows Target Confluence (YENİ EKLENEN SATIRLAR)
            candles = all_symbol_data.get('candles')
            current_price = candles.iloc[-1]['close'] if candles is not None and not candles.empty else 0
            liquidity_analysis = all_symbol_data.get('liquidity_analysis', {})
            eqhl_confluence = self._analyze_eqhl_target_confluence(liquidity_analysis, trade_direction, current_price)
            confluence_result['positive_score'] += eqhl_confluence['score']
            if eqhl_confluence['details']:
                confluence_result['positive_details'].append(eqhl_confluence['details'])
            
            # 4. Power of Three (Po3) Phase Confluence
            po3_analysis = all_symbol_data.get('po3_analysis', {})
            po3_confluence = self._analyze_po3_phase_confluence(po3_analysis, trade_direction)
            confluence_result['positive_score'] += po3_confluence['score']
            if po3_confluence['details']:
                confluence_result['positive_details'].append(po3_confluence['details'])
            
            # 5. Rejection Block Confluence (eğer ana sinyal değilse)
            if main_signal.get('type') != 'REJECTION_BLOCK':
                rb_analysis = all_symbol_data.get('rejection_block_analysis', {})
                rb_confluence = self._analyze_rejection_block_confluence(rb_analysis, trade_direction)
                confluence_result['positive_score'] += rb_confluence['score']
                if rb_confluence['details']:
                    confluence_result['positive_details'].append(rb_confluence['details'])
            
            # 6. VWAP Confluence
            vwap_analysis = all_symbol_data.get('vwap_analysis', {})
            vwap_confluence = self._analyze_vwap_confluence(vwap_analysis, trade_direction)
            confluence_result['positive_score'] += vwap_confluence['score']
            if vwap_confluence['details']:
                confluence_result['positive_details'].append(vwap_confluence['details'])
            
            # 7. NPOC (No Print On Close) Confluence
            npoc_analysis = all_symbol_data.get('npoc_analysis', {})
            npoc_confluence = self._analyze_npoc_confluence(npoc_analysis, trade_direction)
            confluence_result['positive_score'] += npoc_confluence['score']
            if npoc_confluence['details']:
                confluence_result['positive_details'].append(npoc_confluence['details'])
            
            # 8. Supertrend Confluence
            supertrend_analysis = all_symbol_data.get('supertrend_analysis', {})
            supertrend_confluence = self._analyze_supertrend_confluence(supertrend_analysis, trade_direction)
            confluence_result['positive_score'] += supertrend_confluence['score']
            if supertrend_confluence['details']:
                confluence_result['positive_details'].append(supertrend_confluence['details'])

            # 9. Sentiment Confluence (CVD + OI Analizi)
            cvd_analysis = all_symbol_data.get('cvd_analysis')
            oi_analysis = all_symbol_data.get('oi_analysis')
            sentiment_confluence = self._analyze_sentiment_confluence(cvd_analysis, oi_analysis, trade_direction, symbol)
            confluence_result['positive_score'] += sentiment_confluence['score']
            if sentiment_confluence['details']:
                confluence_result['positive_details'].append(sentiment_confluence['details'])

            # 10. Süper POI Confluence Kontrolü
            super_poi_analysis = all_symbol_data.get('super_pois', [])
            super_poi_confluence = self._analyze_super_poi_confluence(super_poi_analysis, trade_direction, main_signal)
            confluence_result['positive_score'] += super_poi_confluence['score']
            if super_poi_confluence['details']:
                confluence_result['positive_details'].append(super_poi_confluence['details'])

            logger.debug(f"[{symbol}] Gelişmiş ICT confluence: +{confluence_result['positive_score']:.1f}")
            return confluence_result
            
        except Exception as e:
            logger.error(f"[{symbol}] Gelişmiş ICT confluence hatası: {e}")
            return {'positive_score': 0.0, 'negative_score': 0.0, 'positive_details': [], 'negative_details': []}

    def _analyze_weak_strong_confluence(self, ws_analysis: Dict[str, Any], trade_direction: str) -> Dict[str, Any]:
        """Weak/Strong Swings confluence analizi."""
        if not ws_analysis:
            return {'score': 0.0, 'details': ''}
            
        labeled_swings = ws_analysis.get('labeled_swings', [])
        if not labeled_swings:
            return {'score': 0.0, 'details': ''}
        
        # Son swing'i al
        last_swing = labeled_swings[-1]
        strength_type = last_swing.get('strength_type', '')
        swing_type = last_swing.get('swing_type', '')
        
        # Long pozisyon için Strong Low veya Weak High faydalı
        if trade_direction == 'bull':
            if 'Strong' in strength_type and 'Low' in swing_type:
                return {'score': 1.0, 'details': '✅ Strong Low Desteği'}
            elif 'Weak' in strength_type and 'High' in swing_type:
                return {'score': 0.8, 'details': '✅ Weak High Hedefi'}
        
        # Short pozisyon için Strong High veya Weak Low faydalı
        else:  # bear
            if 'Strong' in strength_type and 'High' in swing_type:
                return {'score': 1.0, 'details': '✅ Strong High Direnci'}
            elif 'Weak' in strength_type and 'Low' in swing_type:
                return {'score': 0.8, 'details': '✅ Weak Low Hedefi'}
        
        return {'score': 0.0, 'details': ''}

    def _analyze_vwap_confluence(self, vwap_analysis: Dict[str, Any], trade_direction: str) -> Dict[str, Any]:
        """VWAP confluence analizi."""
        if not vwap_analysis:
            return {'score': 0.0, 'details': ''}
        
        vwap_trend = vwap_analysis.get('trend', 'NEUTRAL')
        vwap_distance = vwap_analysis.get('distance_pct', 0)
        
        # VWAP trend ile trade direction uyumu
        if (trade_direction == 'bull' and vwap_trend == 'BULLISH') or \
           (trade_direction == 'bear' and vwap_trend == 'BEARISH'):
            # VWAP'a yakınlık bonusu (daha yakın = daha iyi)
            proximity_bonus = max(0, 1.0 - abs(vwap_distance) / 2.0)  # %2'den fazla uzaklık bonus vermez
            total_score = 1.0 + proximity_bonus
            return {'score': total_score, 'details': f'✅ VWAP {vwap_trend} Trend Uyumu'}
        
        return {'score': 0.0, 'details': ''}

    def _analyze_npoc_confluence(self, npoc_analysis: Dict[str, Any], trade_direction: str) -> Dict[str, Any]:
        """NPOC (No Print On Close) confluence analizi."""
        if not npoc_analysis:
            return {'score': 0.0, 'details': ''}
        
        npoc_levels = npoc_analysis.get('significant_levels', [])
        current_price = npoc_analysis.get('current_price', 0)
        
        if not npoc_levels or not current_price:
            return {'score': 0.0, 'details': ''}
        
        # Yakın NPOC seviyelerini bul (±1% içinde)
        nearby_npoc = []
        for level in npoc_levels:
            level_price = level.get('price', 0)
            if level_price > 0:
                distance_pct = abs(level_price - current_price) / current_price * 100
                if distance_pct <= 1.0:  # %1 içinde
                    nearby_npoc.append(level)
        
        if nearby_npoc:
            # NPOC seviyesi sayısına göre skor
            score = min(2.0, len(nearby_npoc) * 0.8)
            return {'score': score, 'details': f'✅ {len(nearby_npoc)} NPOC Seviyesi Yakın'}
        
        return {'score': 0.0, 'details': ''}

    def _analyze_supertrend_confluence(self, supertrend_analysis: Dict[str, Any], trade_direction: str) -> Dict[str, Any]:
        """Supertrend confluence analizi."""
        if not supertrend_analysis:
            return {'score': 0.0, 'details': ''}
        
        supertrend_direction = supertrend_analysis.get('direction', 'NEUTRAL')
        supertrend_strength = supertrend_analysis.get('strength', 0)
        
        # Supertrend yönü ile trade direction uyumu
        if (trade_direction == 'bull' and supertrend_direction == 'UP') or \
           (trade_direction == 'bear' and supertrend_direction == 'DOWN'):
            # Trend gücüne göre skor (0-1 arası strength değeri bekleniyor)
            strength_score = supertrend_strength * 1.5  # Maksimum 1.5 puan
            return {'score': strength_score, 'details': f'✅ Supertrend {supertrend_direction} Uyumu'}
        
        return {'score': 0.0, 'details': ''}
        
        return {'score': 0.0, 'details': ''}

    def _analyze_sentiment_confluence(self, cvd_analysis: Dict[str, Any], oi_analysis: List[Dict],
                                    trade_direction: str, symbol: str) -> Dict[str, Any]:
        """
        CVD ve Open Interest verilerini kullanarak sentiment confluence analizi yapar.

        Args:
            cvd_analysis: CVD analiz sonuçları (timeframe bazlı DataFrame'ler)
            oi_analysis: Open Interest verisi (liste formatında)
            trade_direction: İşlem yönü ('bull' veya 'bear')
            symbol: Sembol adı

        Returns:
            Dict: Confluence skoru ve detayları
        """
        try:
            if not cvd_analysis or not oi_analysis:
                return {'score': 0.0, 'details': ''}

            # CVD Analyzer'ın squeeze analiz metodunu kullan
            if hasattr(self, 'cvd_analyzer'):
                squeeze_result = self.cvd_analyzer.analyze_oi_cvd_divergence(oi_analysis, cvd_analysis, symbol)
            else:
                # CVD Analyzer yoksa manuel analiz yap
                squeeze_result = self._manual_cvd_oi_analysis(cvd_analysis, oi_analysis, symbol)

            if not squeeze_result:
                return {'score': 0.0, 'details': ''}

            squeeze_type = squeeze_result.get('type', '')
            confidence = squeeze_result.get('confidence', 0)
            details = squeeze_result.get('details', '')

            # Squeeze tipi ile trade direction uyumluluğunu kontrol et
            if trade_direction == 'bull' and 'BULLISH_SQUEEZE' in squeeze_type:
                # Bullish squeeze + bull direction = pozitif confluence
                score = min(2.0, confidence / 10.0)  # Max 2.0 puan
                return {'score': score, 'details': f'✅ CVD/OI Bullish Squeeze: {details}'}

            elif trade_direction == 'bear' and 'BEARISH_SQUEEZE' in squeeze_type:
                # Bearish squeeze + bear direction = pozitif confluence
                score = min(2.0, confidence / 10.0)  # Max 2.0 puan
                return {'score': score, 'details': f'✅ CVD/OI Bearish Squeeze: {details}'}

            else:
                # Squeeze var ama trade direction ile uyumsuz
                return {'score': 0.0, 'details': f'⚠️ CVD/OI Squeeze uyumsuz: {squeeze_type} vs {trade_direction}'}

        except Exception as e:
            logger.error(f"[{symbol}] Sentiment confluence analizi hatası: {e}")
            return {'score': 0.0, 'details': ''}

    def _manual_cvd_oi_analysis(self, cvd_analysis: Dict[str, Any], oi_analysis: List[Dict], symbol: str) -> Optional[Dict]:
        """
        CVD Analyzer yoksa manuel CVD/OI squeeze analizi yapar.
        """
        try:
            if len(oi_analysis) < 2:
                return None

            # OI trend analizi
            recent_oi = float(oi_analysis[-1].get('openInterest', 0))
            previous_oi = float(oi_analysis[-2].get('openInterest', 0))

            if previous_oi == 0:
                return None

            oi_change = ((recent_oi - previous_oi) / previous_oi * 100)

            # CVD trend analizi (4h'i tercih et, yoksa 1h kullan)
            cvd_df = None
            for tf in ['4h', '1h']:
                temp_df = cvd_analysis.get(tf)
                if temp_df is not None and isinstance(temp_df, pd.DataFrame) and not temp_df.empty:
                    cvd_df = temp_df
                    break

            if cvd_df is None or cvd_df.empty or len(cvd_df) < 2:
                return None

            recent_cvd = cvd_df.iloc[-1]['cvd']
            previous_cvd = cvd_df.iloc[-2]['cvd']
            cvd_change = recent_cvd - previous_cvd

            # Squeeze pattern tespiti
            squeeze_threshold = 5.0  # OI değişim eşiği (%)

            # Bullish Squeeze: OI artıyor + CVD düşüyor
            if oi_change > squeeze_threshold and cvd_change < 0:
                return {
                    'type': 'BULLISH_SQUEEZE',
                    'details': f"OI +{oi_change:.1f}%, CVD -{abs(cvd_change):.2f}",
                    'oi_change': oi_change,
                    'cvd_change': cvd_change,
                    'confidence': abs(oi_change) + abs(cvd_change)
                }

            # Bearish Squeeze: OI artıyor + CVD yükseliyor
            elif oi_change > squeeze_threshold and cvd_change > 0:
                return {
                    'type': 'BEARISH_SQUEEZE',
                    'details': f"OI +{oi_change:.1f}%, CVD +{cvd_change:.2f}",
                    'oi_change': oi_change,
                    'cvd_change': cvd_change,
                    'confidence': abs(oi_change) + abs(cvd_change)
                }

            return None

        except Exception as e:
            logger.error(f"[{symbol}] Manuel CVD/OI analizi hatası: {e}")
            return None

    def _analyze_volume_imbalance_confluence(self, vi_analysis: Dict[str, Any], trade_direction: str) -> Dict[str, Any]:
        """Volume Imbalance confluence analizi."""
        if not vi_analysis:
            return {'score': 0.0, 'details': ''}
            
        active_imbalances = vi_analysis.get('active_imbalances', [])
        if not active_imbalances:
            return {'score': 0.0, 'details': ''}
        
        # Yüksek kalite VI'ları bul
        high_quality_vis = [vi for vi in active_imbalances if vi.get('quality_score', 0) >= 7]
        
        # Yön uyumlu VI'ları say
        compatible_vis = []
        for vi in high_quality_vis:
            vi_direction = vi.get('direction', '')
            if ((trade_direction == 'bull' and vi_direction == 'BULLISH') or
                (trade_direction == 'bear' and vi_direction == 'BEARISH')):
                compatible_vis.append(vi)
        
        if compatible_vis:
            score = min(len(compatible_vis) * 0.6, 1.2)  # Her VI için 0.6 puan, max 1.2
            return {'score': score, 'details': f'✅ {len(compatible_vis)} Yüksek Kalite VI Desteği'}
        
        return {'score': 0.0, 'details': ''}

    def _analyze_eqhl_target_confluence(self, liquidity_analysis: Dict[str, Any], trade_direction: str, current_price: float = 0) -> Dict[str, Any]:
        """
        Henüz alınmamış EQL/EQH seviyelerinin bir hedef (draw on liquidity) olup olmadığını analiz eder.
        "Draw on Liquidity" konseptine uygun olarak, birincil sinyalin yönü ile 
        dokunulmamış likidite hedeflerinin uyumlu olup olmadığını kontrol eder.
        """
        # DÜZELTME: Veriyi 'equal_levels' alt sözlüğünden al
        equal_levels = liquidity_analysis.get('equal_levels', {})
        if not equal_levels:
            return {'score': 0.0, 'details': ''}

        if trade_direction == 'bull':
            # Yükseliş trendinde, yukarıdaki bir EQH hedef olarak görülür.
            equal_highs = equal_levels.get('equal_highs', [])
            unbroken_eqh = [eq for eq in equal_highs 
                           if eq.get('price', 0) > current_price and not eq.get('is_broken', False)]
            if unbroken_eqh:
                # En yakın EQH'ı bul
                closest_target = min(unbroken_eqh, key=lambda x: x.get('price', float('inf')))
                return {
                    'score': 1.5, 
                    'details': f"✅ Hedef: Yakındaki EQH Likiditesi @ {closest_target.get('price', 0):.4f}"
                }
                
        elif trade_direction == 'bear':
            # Düşüş trendinde, aşağıdaki bir EQL hedef olarak görülür.
            equal_lows = equal_levels.get('equal_lows', [])
            unbroken_eql = [eq for eq in equal_lows 
                           if eq.get('price', 0) < current_price and not eq.get('is_broken', False)]
            if unbroken_eql:
                # En yakın EQL'i bul
                closest_target = max(unbroken_eql, key=lambda x: x.get('price', 0))
                return {
                    'score': 1.5, 
                    'details': f"✅ Hedef: Yakındaki EQL Likiditesi @ {closest_target.get('price', 0):.4f}"
                }

        return {'score': 0.0, 'details': ''}

    def _analyze_po3_phase_confluence(self, po3_analysis: Dict[str, Any], trade_direction: str) -> Dict[str, Any]:
        """Power of Three phase confluence analizi."""
        if not po3_analysis:
            return {'score': 0.0, 'details': ''}
            
        current_phase = po3_analysis.get('current_phase', '')
        phase_confidence = po3_analysis.get('phase_confidence', 0)
        
        if phase_confidence < 0.6:  # Düşük güven, confluence verme
            return {'score': 0.0, 'details': ''}
        
        # Expansion phase'de trend yönünde işlem yapmak faydalı
        if current_phase == 'EXPANSION':
            return {'score': 0.7, 'details': '✅ Po3 Expansion Fazı'}
        
        # Accumulation'da reversal bekle
        elif current_phase == 'ACCUMULATION':
            return {'score': 0.4, 'details': '⚠️ Po3 Accumulation Fazı'}
        
        return {'score': 0.0, 'details': ''}

    def _analyze_rejection_block_confluence(self, rb_analysis: Dict[str, Any], trade_direction: str) -> Dict[str, Any]:
        """Rejection Block confluence analizi."""
        if not rb_analysis:
            return {'score': 0.0, 'details': ''}
            
        rb_signals = rb_analysis.get('signals', [])
        if not rb_signals:
            return {'score': 0.0, 'details': ''}
        
        # Yön uyumlu rejection block'ları say
        compatible_rbs = []
        for rb in rb_signals:
            rb_type = rb.get('type', '')
            if ((trade_direction == 'bull' and 'Bullish' in rb_type) or
                (trade_direction == 'bear' and 'Bearish' in rb_type)):
                compatible_rbs.append(rb)
        
        if compatible_rbs:
            return {'score': 0.6, 'details': f'✅ {len(compatible_rbs)} Rejection Block Teyidi'}
        
        return {'score': 0.0, 'details': ''}

    def _check_ote_ob_confluence_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        OTE + Order Block Confluence sinyalini kontrol eder.
        Artık merkezi ote_confluence_analyzer modülünü kullanır.

        Args:
            symbol: Sembol adı
            all_symbol_data: Tüm analiz verileri

        Returns:
            OTE+OB confluence sinyali veya None
        """
        try:
            # Merkezi OTE Confluence Analyzer'ı kullan
            if self.ote_confluence_analyzer:
                return self.ote_confluence_analyzer.check_ote_confluence_signal(symbol, all_symbol_data)
            else:
                logger.warning(f"[{symbol}] OTE Confluence Analyzer mevcut değil")
                return None

        except Exception as e:
            logger.error(f"[{symbol}] OTE confluence sinyal kontrolü hatası: {e}", exc_info=True)
            return None

    # ============================================================================
    # YENİ: POTANSİYEL KURULUM ANALİZİ VE BEKLEMEDEKİ SİNYAL YÖNETİMİ
    # ============================================================================
    
    def _analyze_potential_setup_quality(self, symbol: str, all_symbol_data: Dict[str, Any], 
                                        trade_direction: str, main_signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Potansiyel kurulumların kalitesini analiz eder ve "beklemedeki sinyal" konseptini uygular.
        
        Mantık:
        1. BOS sonrası fiyat henüz giriş seviyesine gelmemiş yüksek kaliteli kurulum var mı?
        2. OTE bölgesinde temiz, doldurulmamış FVG var mı?
        3. Fibonacci seviyelerinde POI confluence potansiyeli var mı?
        
        Args:
            symbol: Sembol adı
            all_symbol_data: Tüm analiz verileri
            trade_direction: İşlem yönü
            main_signal: Ana sinyal
            
        Returns:
            Dict: Potansiyel kurulum bilgileri
        """
        try:
            candles = all_symbol_data.get('candles')
            if candles is None or candles.empty:
                return {'setup_potential': 'unknown'}
            
            current_price = float(candles.iloc[-1]['close'])
            result = {
                'setup_potential': 'none',
                'potential_score': 0.0,
                'setup_distance_pct': 0.0,
                'setup_details': [],
                'is_pending_signal': False,
                'pending_reason': '',
                'target_entry_price': None,
                'target_entry_zone': None
            }
            
            # 1. BOS sonrası kurulum analizi
            bos_potential = self._analyze_bos_retracement_potential(
                symbol, all_symbol_data, trade_direction, current_price
            )
            
            if bos_potential:
                result.update(bos_potential)
                
            # 2. Fibonacci + POI potansiyel analizi
            fib_poi_potential = self._analyze_fibonacci_poi_potential(
                symbol, all_symbol_data, trade_direction, current_price
            )
            
            if fib_poi_potential and fib_poi_potential.get('potential_score', 0) > result.get('potential_score', 0):
                result.update(fib_poi_potential)
            
            # 3. HTF POI + LTF MSS potansiyel analizi
            htf_ltf_potential = self._analyze_htf_ltf_mss_potential(
                symbol, all_symbol_data, trade_direction, current_price
            )
            
            if htf_ltf_potential and htf_ltf_potential.get('potential_score', 0) > result.get('potential_score', 0):
                result.update(htf_ltf_potential)
            
            # 4. Genel potansiyel değerlendirmesi
            potential_score = result.get('potential_score', 0)
            
            if potential_score >= 75:
                result['setup_potential'] = 'excellent'
            elif potential_score >= 60:
                result['setup_potential'] = 'good'
            elif potential_score >= 45:
                result['setup_potential'] = 'fair'
            elif potential_score >= 30:
                result['setup_potential'] = 'weak'
            else:
                result['setup_potential'] = 'none'
            
            # 5. Beklemedeki sinyal durumu belirle
            distance_pct = result.get('setup_distance_pct', 0)
            if potential_score >= 60 and 0.5 <= distance_pct <= 5.0:  # %0.5-5 arası uzaklık ideal
                result['is_pending_signal'] = True
                result['pending_reason'] = f"Yüksek kalite kurulum {distance_pct:.1f}% uzakta bekliyor"
                
                logger.info(f"[{symbol}] 🎯 POTANSİYEL KURULUM TESPİT EDİLDİ!")
                logger.info(f"[{symbol}] Kalite: {result['setup_potential'].upper()}")
                logger.info(f"[{symbol}] Skor: {potential_score:.1f}/100")
                logger.info(f"[{symbol}] Uzaklık: {distance_pct:.1f}%")
                logger.info(f"[{symbol}] Hedef Giriş: {result.get('target_entry_price', 'N/A')}")
            
            return result
            
        except Exception as e:
            logger.error(f"[{symbol}] Potansiyel kurulum analizi hatası: {e}")
            return {'setup_potential': 'unknown', 'potential_score': 0.0}
    
    def _analyze_bos_retracement_potential(self, symbol: str, all_symbol_data: Dict[str, Any],
                                          trade_direction: str, current_price: float) -> Optional[Dict[str, Any]]:
        """
        BOS sonrası geri çekilme potansiyelini analiz eder.
        """
        try:
            structure_analysis = all_symbol_data.get('main_tf_structure', {})
            recent_breaks = structure_analysis.get('breaks', [])
            
            if not recent_breaks:
                return None
                
            # En son BOS'u bul
            latest_bos = recent_breaks[-1]
            bos_type = latest_bos.get('type', '')
            
            if 'BOS' not in bos_type:
                return None
                
            # BOS sonrası oluşan impulse leg'i bul
            leg_start = latest_bos.get('leg_start_price')
            leg_end = latest_bos.get('leg_end_price')
            
            if not leg_start or not leg_end:
                return None
            
            # Kapsamlı Fibonacci seviyeleri hesapla
            try:
                # DI ile optimize edildi - her seferinde yeni instance oluşturmak yerine property kullan
                fib_levels = self.fibonacci_analyzer.get_full_retracement_levels(leg_start, leg_end)
                
                if fib_levels.get('error'):
                    return None
                    
            except:
                return None
            
            # OTE bölgesi kontrolü
            ote_zone = fib_levels.get('ote_zone', {})
            if not ote_zone:
                return None
                
            # OTE bölgesinde POI var mı kontrol et
            ote_start = ote_zone.get('entry_start', 0)
            ote_end = ote_zone.get('entry_end', 0)
            sweet_spot = ote_zone.get('sweet_spot', 0)
            
            # Fiyatın OTE'ye olan uzaklığını hesapla
            distance_to_ote = min(abs(current_price - ote_start), abs(current_price - ote_end))
            distance_pct = (distance_to_ote / current_price) * 100
            
            # POI kalitesi analizi
            poi_quality = self._assess_ote_poi_quality(symbol, all_symbol_data, ote_zone, trade_direction)
            
            potential_score = 40  # Base score for BOS retracement potential
            
            # POI kalitesi bonusu
            potential_score += poi_quality.get('score', 0)
            
            # Uzaklık bonusu (ideal uzaklık %1-3 arası)
            if 1.0 <= distance_pct <= 3.0:
                potential_score += 15
            elif 0.5 <= distance_pct <= 5.0:
                potential_score += 10
            elif distance_pct <= 10.0:
                potential_score += 5
            
            return {
                'setup_potential': 'pending_bos_retracement',
                'potential_score': potential_score,
                'setup_distance_pct': distance_pct,
                'target_entry_price': sweet_spot,
                'target_entry_zone': f"OTE {ote_start:.4f}-{ote_end:.4f}",
                'setup_details': [
                    f"BOS sonrası {distance_pct:.1f}% uzaklıkta OTE retracement bekleniyor",
                    f"POI kalitesi: {poi_quality.get('description', 'Bilinmiyor')}"
                ],
                'fibonacci_levels': fib_levels
            }
            
        except Exception as e:
            logger.error(f"[{symbol}] BOS retracement potential analizi hatası: {e}")
            return None
    
    def _analyze_fibonacci_poi_potential(self, symbol: str, all_symbol_data: Dict[str, Any],
                                        trade_direction: str, current_price: float) -> Optional[Dict[str, Any]]:
        """
        Fibonacci seviyeleri + POI potansiyel confluencelarını analiz eder.
        """
        try:
            fibonacci_data = all_symbol_data.get('fibonacci_analysis', {})
            if fibonacci_data.get('error', True):
                return None
            
            # Güncel fiyata en yakın önemli Fibonacci seviyelerini bul
            important_levels = ['0.618', '0.705', '0.5', '0.786', '0.66']
            closest_level = None
            min_distance = float('inf')
            
            # Premium/Discount bölgesinden seviye fiyatlarını al
            equilibrium = fibonacci_data.get('equilibrium', 0)
            range_high = fibonacci_data.get('range_high', 0)
            range_low = fibonacci_data.get('range_low', 0)
            
            if not all([equilibrium, range_high, range_low]):
                return None
            
            range_size = range_high - range_low
            
            for level_key in important_levels:
                level_ratio = float(level_key)
                
                # Fibonacci seviye fiyatını hesapla
                if trade_direction.lower() == 'bullish':
                    level_price = range_high - (range_size * level_ratio)
                else:
                    level_price = range_low + (range_size * level_ratio)
                
                distance = abs(current_price - level_price)
                distance_pct = (distance / current_price) * 100
                
                if distance_pct < min_distance and distance_pct <= 5.0:  # Max %5 uzaklık
                    min_distance = distance_pct
                    closest_level = {
                        'key': level_key,
                        'price': level_price,
                        'distance_pct': distance_pct
                    }
            
            if not closest_level:
                return None
            
            # En yakın seviye etrafında POI kalitesini değerlendir
            poi_quality = self._assess_fibonacci_level_poi_quality(
                symbol, all_symbol_data, closest_level, trade_direction
            )
            
            potential_score = 30  # Base score for Fibonacci potential
            
            # Seviye önem bonusu
            level_importance = {
                '0.618': 20,  # Altın oran
                '0.705': 18,  # Sweet spot
                '0.5': 15,    # Equilibrium
                '0.786': 12,  # OTE sonu
                '0.66': 10    # Golden pocket sonu
            }
            potential_score += level_importance.get(closest_level['key'], 5)
            
            # POI kalitesi bonusu
            potential_score += poi_quality.get('score', 0)
            
            # Uzaklık bonusu
            if closest_level['distance_pct'] <= 1.5:
                potential_score += 15
            elif closest_level['distance_pct'] <= 3.0:
                potential_score += 10
            elif closest_level['distance_pct'] <= 5.0:
                potential_score += 5
            
            return {
                'setup_potential': 'pending_fibonacci_confluence',
                'potential_score': potential_score,
                'setup_distance_pct': closest_level['distance_pct'],
                'target_entry_price': closest_level['price'],
                'target_entry_zone': f"Fibonacci {float(closest_level['key'])*100:.1f}%",
                'setup_details': [
                    f"Fibonacci {closest_level['key']} seviyesi {closest_level['distance_pct']:.1f}% uzaklıkta",
                    f"POI confluence potansiyeli: {poi_quality.get('description', 'Orta')}"
                ]
            }
            
        except Exception as e:
            logger.error(f"[{symbol}] Fibonacci POI potential analizi hatası: {e}")
            return None
    
    def _analyze_htf_ltf_mss_potential(self, symbol: str, all_symbol_data: Dict[str, Any],
                                      trade_direction: str, current_price: float) -> Optional[Dict[str, Any]]:
        """
        HTF POI + LTF MSS kurulum potansiyelini analiz eder.
        """
        try:
            htf_poi_analysis = all_symbol_data.get('htf_poi_ltf_mss', {})
            if not htf_poi_analysis:
                return None
            
            signals = htf_poi_analysis.get('signals', [])
            if not signals:
                return None
            
            # En iyi sinyali bul
            best_signal = max(signals, key=lambda x: x.get('confluence_score', 0))
            confluence_score = best_signal.get('confluence_score', 0)
            
            if confluence_score < 60:  # Minimum kalite eşiği
                return None
            
            # HTF POI bölgesine olan uzaklığı hesapla
            htf_poi_data = best_signal.get('htf_poi_data', {})
            poi_zone = htf_poi_data.get('zone', {})
            
            if poi_zone:
                zone_center = (poi_zone.get('high', 0) + poi_zone.get('low', 0)) / 2
                distance = abs(current_price - zone_center)
                distance_pct = (distance / current_price) * 100
            else:
                distance_pct = 0
            
            potential_score = confluence_score * 0.8  # Base score from confluence
            
            # Uzaklık bonusu
            if distance_pct <= 2.0:
                potential_score += 10
            elif distance_pct <= 5.0:
                potential_score += 5
            
            return {
                'setup_potential': 'pending_htf_poi_ltf_mss',
                'potential_score': potential_score,
                'setup_distance_pct': distance_pct,
                'target_entry_price': zone_center if poi_zone else current_price,
                'target_entry_zone': f"HTF POI {poi_zone.get('low', 0):.4f}-{poi_zone.get('high', 0):.4f}" if poi_zone else "HTF POI",
                'setup_details': [
                    f"HTF POI + LTF MSS confluence: {confluence_score:.1f}/100",
                    f"POI bölgesi {distance_pct:.1f}% uzaklıkta"
                ]
            }
            
        except Exception as e:
            logger.error(f"[{symbol}] HTF LTF MSS potential analizi hatası: {e}")
            return None
    
    def _assess_ote_poi_quality(self, symbol: str, all_symbol_data: Dict[str, Any],
                               ote_zone: Dict[str, float], trade_direction: str) -> Dict[str, Any]:
        """
        OTE bölgesi içindeki POI kalitesini değerlendirir.
        Artık merkezi ote_confluence_analyzer modülünü kullanır.
        """
        try:
            # Merkezi OTE Confluence Analyzer'ı kullan
            if self.ote_confluence_analyzer:
                return self.ote_confluence_analyzer.assess_ote_poi_quality(symbol, all_symbol_data, ote_zone, trade_direction)
            else:
                logger.warning(f"[{symbol}] OTE Confluence Analyzer mevcut değil")
                return {'score': 0, 'description': 'OTE Analyzer mevcut değil'}

        except Exception as e:
            logger.error(f"[{symbol}] OTE POI kalite değerlendirme hatası: {e}")
            return {'score': 0, 'description': 'Değerlendirme hatası'}
    
    def _assess_fibonacci_level_poi_quality(self, symbol: str, all_symbol_data: Dict[str, Any],
                                           closest_level: Dict[str, Any], trade_direction: str) -> Dict[str, Any]:
        """
        Fibonacci seviyesi etrafındaki POI kalitesini değerlendirir.
        Artık merkezi ote_confluence_analyzer modülünü kullanır.
        """
        try:
            # Merkezi OTE Confluence Analyzer'ı kullan
            if self.ote_confluence_analyzer:
                return self.ote_confluence_analyzer.assess_fibonacci_level_poi_quality(symbol, all_symbol_data, closest_level, trade_direction)
            else:
                logger.warning(f"[{symbol}] OTE Confluence Analyzer mevcut değil")
                return {'score': 0, 'description': 'OTE Analyzer mevcut değil'}

        except Exception as e:
            logger.error(f"[{symbol}] Fibonacci POI kalite değerlendirme hatası: {e}")
            return {'score': 0, 'description': 'Hata'}
    def _check_turtle_soup_ifvg_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Turtle Soup + IFVG confluence sinyallerini kontrol eder.
        
        Turtle Soup + IFVG Konsepti:
        - False breakout (turtle soup) + Inverse Fair Value Gap confluence
        - Çok güçlü reversal sinyali
        - Yüksek R:R oranı potansiyeli
        
        Args:
            symbol: Sembol adı
            all_symbol_data: Tüm analiz verileri
            
        Returns:
            Optional[Dict]: Turtle Soup + IFVG sinyali veya None
        """
        try:
            turtle_soup_analysis = all_symbol_data.get('turtle_soup_ifvg', {})
            turtle_soup_signals = turtle_soup_analysis.get('signals', [])
            
            if not turtle_soup_signals:
                logger.debug(f"[{symbol}] Turtle Soup + IFVG sinyali bulunamadı")
                return None
            
            # En yüksek confluence skorlu sinyali seç
            best_signal = max(turtle_soup_signals, key=lambda x: x.get('confluence_score', 0))
            confluence_score = best_signal.get('confluence_score', 0)
            
            # Minimum confluence score kontrolü
            if confluence_score < 70.0:
                logger.debug(f"[{symbol}] Turtle Soup + IFVG confluence skoru düşük: {confluence_score:.1f}")
                return None
            
            # Turtle Soup ve IFVG verilerini al
            turtle_soup_data = best_signal.get('turtle_soup_data', {})
            ifvg_data = best_signal.get('ifvg_data', {})
            
            # Confidence hesapla
            turtle_soup_strength = turtle_soup_data.get('strength', 5)
            ifvg_strength = ifvg_data.get('inversion_strength', 5)
            
            # Base confidence: confluence score'a göre
            base_confidence = min(0.95, 0.6 + (confluence_score / 200))
            
            # Strength bonus
            avg_strength = (turtle_soup_strength + ifvg_strength) / 2
            if avg_strength >= 8:
                base_confidence = min(0.95, base_confidence + 0.1)
            elif avg_strength >= 6:
                base_confidence = min(0.95, base_confidence + 0.05)
            
            # Direction ve price bilgileri
            direction = best_signal.get('direction', 'UNKNOWN').lower()
            entry_price = best_signal.get('soup_level', 0) or best_signal.get('ifvg_level', 0)
            
            logger.success(f"[{symbol}] 🐢 Turtle Soup + IFVG sinyali bulundu: "
                          f"{direction.upper()} (Confluence: {confluence_score:.1f}, "
                          f"Soup Strength: {turtle_soup_strength:.1f}, "
                          f"IFVG Strength: {ifvg_strength:.1f})")
            
            return {
                'type': 'TURTLE_SOUP_IFVG',
                'symbol': symbol,
                'direction': direction,
                'price': entry_price,
                'confidence': base_confidence,
                'confluence_score': base_confidence * 100,
                'reason': f'Turtle Soup + IFVG Confluence (Score: {confluence_score:.1f})',
                'turtle_soup_strength': turtle_soup_strength,
                'ifvg_strength': ifvg_strength,
                'turtle_soup_data': turtle_soup_data,
                'ifvg_data': ifvg_data,
                'soup_level': best_signal.get('soup_level', 0),
                'ifvg_level': best_signal.get('ifvg_level', 0),
                'proximity_pct': best_signal.get('proximity_pct', 0),
                'entry_method': 'turtle_soup_ifvg',
                'pattern': 'TURTLE_SOUP_IFVG'
            }
            
        except Exception as e:
            logger.error(f"[{symbol}] Turtle Soup + IFVG sinyal kontrolü hatası: {e}", exc_info=True)
            return None

    def _check_breaker_retest_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Breaker Block'ların yeniden test edilmesini kontrol eder.
        Breaker Block'lar Süper POI bölgelerinde bulunduğunda çok güçlü sinyaller verir.
        
        Args:
            symbol (str): Ticaret sembolü.
            all_symbol_data (Dict[str, Any]): Sembole ait tüm analiz verileri.

        Returns:
            Optional[Dict[str, Any]]: Geçerli bir Breaker Block retest sinyali veya None.
        """
        try:
            super_pois = all_symbol_data.get('super_pois', [])
            candles = all_symbol_data.get('candles')
            
            if not super_pois or candles is None or candles.empty:
                return None

            current_price = candles.iloc[-1]['close']

            for poi in super_pois:
                # POI içinde breaker block var mı kontrol et
                has_breaker = any(factor == 'breaker_block' for factor in poi.get('confluent_factors', []))
                
                if has_breaker:
                    poi_top = poi.get('super_poi_top')
                    poi_bottom = poi.get('super_poi_bottom')
                    
                    # Fiyat POI'ye yakın mı kontrol et (örneğin %0.5 tolerans)
                    poi_mid = (poi_top + poi_bottom) / 2
                    distance_pct = abs(current_price - poi_mid) / current_price
                    
                    if distance_pct < 0.005:  # %0.5 tolerans
                        # Confluence score'a göre confidence hesapla
                        confluence_score = poi.get('confluence_score', 0)
                        base_confidence = min(0.95, 0.6 + (confluence_score / 200))
                        
                        # Breaker block'lar genellikle güvenilir olduğu için bonus
                        base_confidence = min(0.95, base_confidence + 0.1)
                        
                        direction = poi.get('direction', 'UNKNOWN')
                        direction_str = direction.upper() if direction else 'UNKNOWN'
                        logger.success(f"[{symbol}] 🔄 Breaker Block Retest sinyali bulundu: "
                                      f"{direction_str} "
                                      f"(Confluence Score: {confluence_score:.1f})")
                        
                        return {
                            'type': 'BREAKER_RETEST',
                            'symbol': symbol,
                            'direction': poi.get('direction'),
                            'price': current_price,
                            'confidence': base_confidence,
                            'confluence_score': base_confidence * 100,
                            'reason': 'Retest of a Breaker Block within a Super POI',
                            'details': poi,
                            'poi_top': poi_top,
                            'poi_bottom': poi_bottom,
                            'distance_pct': distance_pct,
                            'entry_method': 'breaker_retest',
                            'pattern': 'BREAKER_RETEST'
                        }
            
            return None
            
        except Exception as e:
            logger.error(f"[{symbol}] Breaker Block retest sinyali kontrolü hatası: {e}", exc_info=True)
            return None

    def _analyze_super_poi_confluence(self, super_pois: List[Dict[str, Any]], trade_direction: str, main_signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ana sinyalin giriş fiyatının, yüksek skorlu bir "Süper POI" bölgesiyle
        kesişip kesişmediğini analiz eder ve buna göre bir confluence puanı verir.
        """
        if not super_pois or not main_signal.get('price'):
            return {'score': 0.0, 'details': ''}

        entry_price = main_signal.get('price')

        # Sinyal yönü ile uyumlu ve yüksek skorlu Süper POI'leri bul
        compatible_pois = [
            poi for poi in super_pois
            if poi.get('direction', '').lower() == trade_direction.lower() and poi.get('confluence_score', 0) > 3.0
        ]

        if not compatible_pois:
            return {'score': 0.0, 'details': ''}

        # Giriş fiyatına en yakın, uyumlu ve yüksek skorlu POI'yi bul
        best_poi = min(
            compatible_pois,
            key=lambda poi: abs(entry_price - ((poi.get('super_poi_top', 0) + poi.get('super_poi_bottom', 0)) / 2))
        )

        # Fiyat POI içinde mi kontrol et
        poi_top = best_poi.get('super_poi_top', 0)
        poi_bottom = best_poi.get('super_poi_bottom', 0)

        if poi_bottom <= entry_price <= poi_top:
            score = 2.0  # Temel Süper POI puanı
            details = f"✅ Süper POI Kesişimi (Skor: {best_poi['confluence_score']:.2f})"
            return {'score': score, 'details': details}

        return {'score': 0.0, 'details': ''}